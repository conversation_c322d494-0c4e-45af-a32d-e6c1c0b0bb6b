package com.siact.energy.cal.tool.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.siact.energy.cal.common.pojo.dto.dataTrans.DataTransInsertDTO;
import com.siact.energy.cal.tool.entity.dataTrans.DataTrans;
import com.siact.energy.cal.tool.service.dataTrans.DataTransService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 自动填充功能测试
 * 验证creator和updater字段是否能够自动填充
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class AutoFillTest {

    @Autowired
    private DataTransService dataTransService;

    @Autowired
    private BaseService baseService;

    @Test
    public void testAutoFillCreatorAndUpdater() {
        // 获取当前登录用户ID
        Long currentUserId = baseService.getLoginUserId();
        log.info("当前登录用户ID: {}", currentUserId);

        // 创建测试数据
        DataTransInsertDTO dto = new DataTransInsertDTO();
        dto.setProjectId(10000L);
        dto.setHost("localhost");
        dto.setPort("1883");
        dto.setUserName("testuser");
        dto.setPassword("testpass");
        dto.setTopic("test/topic");

        // 保存数据
        Boolean result = dataTransService.save(dto);
        log.info("保存结果: {}", result);

        // 查询刚刚保存的数据
        LambdaQueryWrapper<DataTrans> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DataTrans::getTopic, dto.getTopic())
                   .eq(DataTrans::getHost, dto.getHost())
                   .orderByDesc(DataTrans::getCreateTime)
                   .last("LIMIT 1");
        DataTrans savedEntity = dataTransService.getOne(queryWrapper);

        if (savedEntity != null) {
            log.info("保存的实体: {}", savedEntity);
            log.info("creator字段: {}, updater字段: {}", savedEntity.getCreator(), savedEntity.getUpdater());

            // 验证自动填充是否生效
            assert savedEntity.getCreator() != null : "creator字段未自动填充";
            assert savedEntity.getUpdater() != null : "updater字段未自动填充";

            if (currentUserId != null) {
                assert savedEntity.getCreator().equals(currentUserId) : "creator字段填充值不正确";
                assert savedEntity.getUpdater().equals(currentUserId) : "updater字段填充值不正确";
            }
        } else {
            log.error("未找到保存的实体");
            assert false : "未找到保存的实体";
        }
    }
}
