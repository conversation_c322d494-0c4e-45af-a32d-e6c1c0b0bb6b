package com.siact.energy.cal.server.service.energycal;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.pojo.dto.energycal.*;
import com.siact.energy.cal.common.pojo.enums.CalTypeEnum;
import com.siact.energy.cal.common.pojo.enums.ConstantBase;
import com.siact.energy.cal.common.pojo.enums.RuleTypeEnum;
import com.siact.energy.cal.common.pojo.enums.TimeUnit;
import com.siact.energy.cal.common.pojo.vo.energycal.*;
import com.siact.energy.cal.common.util.utils.*;
import com.siact.energy.cal.server.common.config.IOThreadPoolConfig;
import com.siact.energy.cal.server.common.pool.ThreadPoolHolder;
import com.siact.energy.cal.server.common.utils.TimeUtils;
import com.siact.energy.cal.server.service.dataSource.DataSourceService;
import com.siact.energy.cal.server.service.ruleDetail.RuleDetailInstanceService;
import com.siact.energy.cal.server.service.ruleDetail.impl.RuleDetailServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/* @Package com.siact.energycal.service
 * @description:
 * <AUTHOR>
 * @create 2024/8/2 16:25
 */
@Service
@Slf4j
public class EnergyCalService {
    private static final Pattern FUNCTION_PATTERN = Pattern.compile("(\\w+)\\(@\\[(\\w+)\\]\\)");
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    @Value("${digitalTwin.api.url}")
    private String dataTwinsUrl;
    private static final AtomicInteger counter = new AtomicInteger(0);
    @Autowired
    @Qualifier(IOThreadPoolConfig.IO_THREAD_POOL_NAME)
    private Executor executor;
    @Autowired
    private RuleDetailServiceImpl ruleDetailService;
    @Autowired
    private DataBaseService dataBaseService;
    @Autowired
    private RuleDetailInstanceService ruleDetailInstanceService;
    @Autowired
    private DataSourceService dataSourceService;


    /*
     * <AUTHOR>
     * @Description //等时间间隔计算
     * @Date 11:22 2024/12/9
     * @Param
     * @return
     **/

    public List<DataIntervalQueryVo> getEquallySpacedTimeData(TimeQueryDTO timeQueryDTO) {
        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap = new ConcurrentHashMap<>();
        queryTimeData(resultMap, timeQueryDTO);
        timeQueryDTO.setTsUnit(convertTsUnit(timeQueryDTO.getTsUnit()));
        return returnEquallySpacedResult(resultMap, timeQueryDTO);
    }


    /*
     * <AUTHOR>
     * @Description // 时间区间计算
     * @Date 11:14 2024/12/9
     * @Param
     * @return
     **/
    public List<DataPointQueryVo> getTimeIntervalData(TimeQueryDTO timeQueryDTO) {
        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap = new ConcurrentHashMap<>();
        queryTimeData(resultMap, timeQueryDTO);
        return returnIntervalResult(resultMap, timeQueryDTO);
    }

    private void getStaticPropertyData(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, List<String> staticPropertyList, List<String[]> timeGroups) {
        if (staticPropertyList.size() > 0) {
            String url = dataTwinsUrl + "/prop/all/rt";
            JSONArray array = new JSONArray();
            staticPropertyList.forEach(item -> array.add(item));
            String result = null;
            try {
                result = HttpClientUtil.postJson(url, array);
            } catch (IOException e) {
                log.error("获取静态属性数据失败");
                throw new RuntimeException(e);
            }
            JSONArray resultArray = CommonUtils.returnResultHandler(result);
            resultArray.stream()
                    .forEach(data -> {
                        JSONObject jsonObject = (JSONObject) data;
                        String dataCode = jsonObject.getString("dataCode");
                        String propVal = jsonObject.getString("propVal");
                        if (StrUtil.isNotBlank(propVal)) {
                            BigDecimal value = new BigDecimal(propVal);
                            timeGroups.forEach(time -> {
                                resultMap.computeIfAbsent(dataCode, k -> new ConcurrentHashMap<>())
                                        .put(time[0], value);
                                if (time[0].equals(timeGroups.get(timeGroups.size() - 1)[0]) && StrUtil.isNotBlank(time[1])) {
                                    //说明是最后一个元素
                                    resultMap.computeIfAbsent(dataCode, k -> new ConcurrentHashMap<>())
                                            .put(time[1], value);
                                }
                            });
                        }
                    });
        }
    }


    /*
     * <AUTHOR>
     * @Description // 根据传入的devpropertyList列表和起止时间计算基础指标结果并将结果存入数据库
     * @Date 15:27 2024/8/10
     * @Param
     * @return
     **/

    public void calBaseIndicatorByDevpropertyCode(TimeQueryDTO timeQueryDTO) {
        // 校验
        validateTimeQuery(timeQueryDTO);
        // 将步长单位转化为TDengine函数库的步长单位
        timeQueryDTO.setTsUnit(convertTsUnit(timeQueryDTO.getTsUnit()));

        // 将devpropertyList按照项目分组,分组后的组成一个map,key是项目编码，value是devpropertyList
        List<String> formulaVarList = getFormulaVarList(timeQueryDTO.getDataCodes());
        Map<String, List<String>> projectDevpropertyMap = groupDevpropertyByprojectCode(formulaVarList);
        Long ruleColID = ConstantBase.RULECOLID_COMMON;
        projectDevpropertyMap.entrySet().forEach(entry -> {
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap = new ConcurrentHashMap<>();
            // 1. 获取数据源配置
            String projectCode = entry.getKey();
            List<String> devproperList = entry.getValue();
            DataSourceVo dataSourceVo = dataSourceService.getDataSourceConfig(projectCode);
            if (ObjectUtils.isEmpty(dataSourceVo)) {
                log.error("未找到项目{}的数据源配置", projectCode);
                return;
            }

            // 2. 获取计算类型配置
            Map<Integer, List<String>> calTypeConfigMap = ruleDetailInstanceService.getCalTypeConfigMap(projectCode, ruleColID);
            // 3. 按计算类型分组属性
            Map<String, List<String>> calTypePropMap = groupDevpropertyByCalType(devproperList, calTypeConfigMap);

            // 4. 获取时间分组
            List<String[]> timeGroups = generateTimeGroups(timeQueryDTO.getStartTime(), timeQueryDTO.getEndTime(),
                    timeQueryDTO.getInterval(), timeQueryDTO.getTsUnit());

            // 5. 处理基础指标
            processBaseIndicators(resultMap, calTypePropMap, timeQueryDTO, dataSourceVo, timeGroups, ConstantBase.EQUALLY_INTERVAL_CAL);

            //6.写入数据库
            dataBaseService.insertData(resultMap, timeQueryDTO.getDataCodes(), dataSourceVo);
        });


    }


    private void calBaseIndicator(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                  DataSourceVo dataSourceVo,
                                  TimeQueryDTO queryDataByIntervalTimeDTO,
                                  String flag,
                                  List<String[]> timeGroups) {
        // 1. 准备计算公式
        List<String> dataCodes = queryDataByIntervalTimeDTO.getDataCodes();
        ArrayList<FormulaClass> formulaList = new ArrayList<>();
        for (String targetProp : dataCodes) {
            String formula = ruleDetailInstanceService.getFormula(targetProp, ConstantBase.RULECOLID_COMMON);
            if (StringUtils.isNotBlank(formula)) {
                formulaList.add(new FormulaClass(targetProp, formula));
            }
        }

        // 2. 对计算公式进行排序
        List<String> orderFormulaDevProp = FormulaUtils.calculateOrder(formulaList);
        // 3. 转换数据结构
        Map<String, Map<String, BigDecimal>> tsPropMap = transformMap(resultMap);

        // 4. 执行并行计算
        try {
            executeParallelCalculation(
                    resultMap,
                    dataSourceVo,
                    dataCodes,
                    orderFormulaDevProp,
                    timeGroups,
                    tsPropMap,
                    flag
            );
        } catch (Exception e) {
            log.error("基础指标计算失败: {}", e.getMessage(), e);
            throw new BizException("基础指标计算失败: " + e.getMessage());
        }
    }


    /**
     * 执行并行计算
     */
    private void executeParallelCalculation(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            DataSourceVo dataSourceVo,
            List<String> dataCodes,
            List<String> orderedProps,
            List<String[]> timeGroups,
            Map<String, Map<String, BigDecimal>> tsPropMap,
            String flag) {
        // 使用计数器限制并发数
        Semaphore semaphore = new Semaphore(20);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (String[] timeGroup : timeGroups) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    semaphore.acquire();
                    processTimeGroup(resultMap, dataSourceVo, dataCodes, orderedProps,
                            timeGroup, tsPropMap, flag);
                } catch (Exception e) {
                    log.error("时间段[{}-{}]计算失败: {}",
                            timeGroup[0], timeGroup[1], e.getMessage(), e);
                    throw new CompletionException(e);
                } finally {
                    semaphore.release();
                }
            }, executor);

            futures.add(future);
        }

        // 等待所有计算完成并统一处理异常
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (CompletionException e) {
            throw new BizException("并行计算失败: " + e.getMessage());
        }
    }

    private void processTimeGroup(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            DataSourceVo dataSourceVo,
            List<String> dataCodes,
            List<String> orderedProps,
            String[] timeGroup,
            Map<String, Map<String, BigDecimal>> tsPropMap,
            String flag) {

        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> queryResultMap =
                splitMapByTimeRanges(tsPropMap, timeGroup);

        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> calMap =
                calBasePropertyTaskResult(queryResultMap, dataSourceVo, dataCodes,
                        orderedProps);

        // 合并结果
        synchronized (resultMap) {
            mergeResults(resultMap, calMap);
        }
    }

    private ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> calBasePropertyTaskResult(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> queryResultMap,
                                                                                                       DataSourceVo dataSourceVo,
                                                                                                       List<String> devpropertyList,
                                                                                                       List<String> orderFormulaDevProp) {
        long start = System.currentTimeMillis();
        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap = new ConcurrentHashMap<>();
        // 计算
        for (ConcurrentHashMap.Entry<String, ConcurrentHashMap<String, BigDecimal>> tsProp : queryResultMap.entrySet()) {
            String ts = tsProp.getKey();
            Map<String, BigDecimal> propVal = tsProp.getValue();
            for (String propCode : orderFormulaDevProp) {
                if (devpropertyList.contains(propCode)) {
                    // 需要计算
                    String formula = ruleDetailInstanceService.getFormula(propCode, ConstantBase.RULECOLID_COMMON);
                    if (StringUtils.isNotBlank(formula)) {
                        List<String> varList = FormulaUtils.getVarList(formula);
                        Map<String, BigDecimal> calValueMap = new ConcurrentHashMap<>();
                        //计算结果，并将结果放入propValueMap中
                        for (String prop : varList) {
                            //获取属性值
                            BigDecimal propTsValue = propVal.getOrDefault(prop, null);
                            if (!ObjectUtils.isEmpty(propTsValue)) {
                                calValueMap.put(prop, propTsValue);
                            }
                        }
                        BigDecimal bigDecimal = FormulaUtils.calcFormula(formula, calValueMap);
                        if (ObjectUtil.isNotEmpty(bigDecimal)) {
                            resultMap.computeIfAbsent(propCode, k -> new ConcurrentHashMap<>())
                                    .put(ts, bigDecimal);
                            queryResultMap.computeIfAbsent(ts, k -> new ConcurrentHashMap<>())
                                    .put(propCode, bigDecimal);
                        }

                    }
                }
            }
        }
        log.info("计算基础指标任务完成,耗时：" + (System.currentTimeMillis() - start) / 1000 + "s");
        // 打印线程编号和当前时间，以检查执行顺序
        int threadId = counter.incrementAndGet();
        log.info("Thread " + threadId + " executing at " + System.currentTimeMillis());
        return resultMap;
    }

    /**
     * 合并计算结果
     */
    private void mergeResults(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                              ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> calMap) {
        calMap.forEach((prop, timeValues) -> {
            resultMap.compute(prop, (k, existingValues) -> {
                if (existingValues == null) {
                    return timeValues;
                }
                existingValues.putAll(timeValues);
                return existingValues;
            });
        });
    }

    public static ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> splitMapByTimeRanges(
            Map<String, Map<String, BigDecimal>> tsProMap,
            String[] timeRanges) {

        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> result = new ConcurrentHashMap<>();

        try {
            // 有开始和结束时间的情况 - 时间范围查询
            if (timeRanges[0] != null && timeRanges[1] != null) {
                LocalDate startDate = LocalDate.parse(timeRanges[0], DateUtils.dateTimeFormatter);
                LocalDate endDate = LocalDate.parse(timeRanges[1], DateUtils.dateTimeFormatter);

                for (Map.Entry<String, Map<String, BigDecimal>> entry : tsProMap.entrySet()) {
                    LocalDate date = LocalDate.parse(entry.getKey(), DateUtils.dateTimeFormatter);

                    // 在时间范围内的数据
                    if (!date.isBefore(startDate) && !date.isAfter(endDate)) {
                        ConcurrentHashMap<String, BigDecimal> valueMap = new ConcurrentHashMap<>();
                        valueMap.putAll(entry.getValue());
                        result.put(entry.getKey(), valueMap);
                    }
                }
            }
            // 只有开始时间的情况 - 时间断面查询
            else if (timeRanges[0] != null && timeRanges[1] == null) {
                // 直接复制所有数据，因为tsProMap已经包含了断面查询所需的时间范围数据
                for (Map.Entry<String, Map<String, BigDecimal>> entry : tsProMap.entrySet()) {
                    ConcurrentHashMap<String, BigDecimal> valueMap = new ConcurrentHashMap<>();
                    valueMap.putAll(entry.getValue());
                    result.put(entry.getKey(), valueMap);
                }
            }
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date format", e);
        }

        return result;
    }


    public void calBaseIndicatorByProjectCode(TimeQueryDTO timeQueryDTO) {
        // 校验
        validateTimeQuery(timeQueryDTO);
        // 将步长单位转化为TDengine函数库的步长单位
        timeQueryDTO.setTsUnit(convertTsUnit(timeQueryDTO.getTsUnit()));
        //获取项目编码对应的计算基础指标属性列表
        Map<String, List<String>> projectBasePropertyMap = getProjectBasePropertyList(timeQueryDTO.getDataCodes());
        Long ruleColID = ConstantBase.RULECOLID_COMMON;
        projectBasePropertyMap.entrySet().forEach(entry -> {
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap = new ConcurrentHashMap<>();
            // 1. 获取数据源配置
            String projectCode = entry.getKey();
            List<String> devproperList = entry.getValue();
            DataSourceVo dataSourceVo = dataSourceService.getDataSourceConfig(projectCode);
            if (ObjectUtils.isEmpty(dataSourceVo)) {
                log.error("未找到项目{}的数据源配置", projectCode);
                return;
            }

            // 2. 获取计算类型配置
            Map<Integer, List<String>> calTypeConfigMap = ruleDetailInstanceService.getCalTypeConfigMap(projectCode, ruleColID);
            // 3. 按计算类型分组属性
            Map<String, List<String>> calTypePropMap = groupDevpropertyByCalType(devproperList, calTypeConfigMap);

            // 4. 获取时间分组
            List<String[]> timeGroups = generateTimeGroups(timeQueryDTO.getStartTime(), timeQueryDTO.getEndTime(),
                    timeQueryDTO.getInterval(), timeQueryDTO.getTsUnit());

            // 5. 处理基础指标
            processBaseIndicators(resultMap, calTypePropMap, timeQueryDTO, dataSourceVo, timeGroups, ConstantBase.EQUALLY_INTERVAL_CAL);

            //6.写入数据库
            dataBaseService.insertData(resultMap, timeQueryDTO.getDataCodes(), dataSourceVo);
        });
    }


    private Map<String, List<String>> getProjectBasePropertyList(List<String> projectCodeList) {
        Map<String, List<String>> projectMap = new HashMap<>();
        for (String projectCode : projectCodeList) {
            Map<Integer, List<String>> calTypeFormulaList = ruleDetailInstanceService.getCalTypeConfigMap(projectCode, ConstantBase.RULECOLID_COMMON);
            if (!ObjectUtils.isEmpty(calTypeFormulaList)) {
                List<String> baseProprtyList = calTypeFormulaList.getOrDefault(ConstantBase.CAL_BASE_TYPE, null);
                if (!ObjectUtils.isEmpty(baseProprtyList)) {
                    List<String> formulaVarList = getFormulaVarList(baseProprtyList);
                    projectMap.put(projectCode, formulaVarList);
                }
            }
        }
        return projectMap;
    }


    public static String convertTsUnit(String tsUnit) {
        if (tsUnit == null) {
            return null;
        }
        String result = null;
        switch (tsUnit) {
            case "Y":
                result = "y";
                break;
            case "M":
                result = "n";
                break;
            case "D":
                result = "d";
                break;
            case "H":
                result = "h";
                break;
            case "MIN":
                result = "m";
                break;
        }
        return result;
    }


    public static Map<String, Map<String, BigDecimal>> transformMap(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> originalMap) {
        Map<String, Map<String, BigDecimal>> transformedMap = new ConcurrentHashMap<>();

        // 遍历originalMap
        for (ConcurrentHashMap.Entry<String, ConcurrentHashMap<String, BigDecimal>> entry : originalMap.entrySet()) {
            String property = entry.getKey();
            Map<String, BigDecimal> timeValueMap = entry.getValue();

            // 遍历内层Map
            for (Map.Entry<String, BigDecimal> timeValueEntry : timeValueMap.entrySet()) {
                String time = timeValueEntry.getKey();
                BigDecimal value = timeValueEntry.getValue();

                // 在转换后的Map中创建或更新对应的映射
                transformedMap.computeIfAbsent(time, t -> new ConcurrentHashMap<>()).put(property, value);
            }
        }

        return transformedMap;
    }

    public List<DataPointQueryVo> getTimeSliceData(QueryDataByPointTimeDTO queryDataByPointTimeDTO) {

        StopWatch stopWatch = new StopWatch();
        stopWatch.start("获取时间断面数据");
        Long ruleColID = ConstantBase.RULECOLID_COMMON;
        try {
            // 1. 参数验证和预处理
            validateAndPrepareParams(queryDataByPointTimeDTO);

            // 2. 初始化结果存储
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap = new ConcurrentHashMap<>();

            // 3. 获取计算所需的所有属性变量
            List<String> dataCodes = queryDataByPointTimeDTO.getDataCodes();
            //递归获取所有涉及到的公式参数
            List<String> formulaVarList = getFormulaVarList(dataCodes);

            // 4. 构建查询参数
            TimeQueryDTO queryParams = buildQueryParams(queryDataByPointTimeDTO);

            // 4.1 按项目分组并处理
            Map<String, List<String>> projectMap = groupDevpropertyByprojectCode(formulaVarList);

            projectMap.entrySet().forEach(entry -> {
                processTimeSliceData(entry.getKey(), entry.getValue(), resultMap, queryParams, ruleColID);
            });

            // 6. 格式化结果
            List<DataPointQueryVo> result = formatTimeSliceResult(dataCodes, resultMap, queryDataByPointTimeDTO.getTs());

            stopWatch.stop();
            log.info("时间断面数据处理完成，总耗时：{} ms", stopWatch.getTotalTimeMillis());
            return result;

        } catch (Exception e) {
            log.error("获取时间断面数据失败: {}", e.getMessage(), e);
            throw new BizException("获取时间断面数据失败: " + e.getMessage());
        }
    }

    private void processTimeSliceData(String projectCode, List<String> dataCodes, ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, TimeQueryDTO queryDTO, Long ruleColID) {
        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("处理项目时间截面数据: " + projectCode);

            // 1. 获取数据源配置
            DataSourceVo dataSourceVo = dataSourceService.getDataSourceConfig(projectCode);
            if (ObjectUtils.isEmpty(dataSourceVo)) {
                log.error("未找到项目{}的数据源配置", projectCode);
                return;
            }

            // 2. 获取计算类型配置
            Map<Integer, List<String>> calTypeConfigMap = ruleDetailInstanceService.getCalTypeConfigMap(projectCode, ruleColID);

            // 3. 按计算类型分组属性
            Map<String, List<String>> calTypePropMap = groupDevpropertyByCalType(dataCodes, calTypeConfigMap);

            // 5. 处理基础指标
            List<String[]> timeGroups = new ArrayList<>();
            processBaseIndicators(resultMap, calTypePropMap, queryDTO, dataSourceVo, timeGroups, ConstantBase.POINT_CAL);
            stopWatch.stop();
            log.info("项目{}数据处理完成, 耗时: {}ms", projectCode, stopWatch.getTotalTimeMillis());

        } catch (Exception e) {
            log.error("处理项目{}数据失败", projectCode, e);
        }
    }


    /**
     * 验证和准备参数
     */
    private void validateAndPrepareParams(QueryDataByPointTimeDTO queryParams) {
        if (CollectionUtils.isEmpty(queryParams.getDataCodes())) {
            throw new BizException("查询属性不能为空");
        }

        // 处理时间参数
        if (StringUtils.isBlank(queryParams.getTs())) {
            queryParams.setTs(DateUtils.getCurrentimeStr());
        }

        if (!DateUtils.isTimeFormatValid(queryParams.getTs(), DateUtils.YMD_HMS)) {
            throw new BizException("时间格式不正确");
        }
    }

    /**
     * 构建查询参数
     */
    private TimeQueryDTO buildQueryParams(QueryDataByPointTimeDTO pointTimeDTO) {
        TimeQueryDTO intervalTimeDTO = new TimeQueryDTO();
        intervalTimeDTO.setDataCodes(pointTimeDTO.getDataCodes());
        intervalTimeDTO.setStartTime(pointTimeDTO.getTs());
        return intervalTimeDTO;
    }


    /**
     * 格式化时间断面结果
     */
    private List<DataPointQueryVo> formatTimeSliceResult(List<String> dataCodes,
                                                         ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                                         String timestamp) {

        return dataCodes.stream()
                .map(dataCode -> {
                    DataPointQueryVo vo = new DataPointQueryVo();
                    vo.setDataCode(dataCode);
                    vo.setTimestamp(timestamp);

                    ConcurrentHashMap<String, BigDecimal> valueMap = resultMap.get(dataCode);
                    if (valueMap != null && !valueMap.isEmpty()) {
                        valueMap.values().forEach(val -> vo.setPropVal(val.toString()));
                    }

                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 使用并行流处理大量数据
     */
    private List<DataPointQueryVo> processWithParallelStream(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            String startTime,
            String endTime,
            List<String> dataCodes) {

        return dataCodes.parallelStream()
                .map(dataCode -> buildDataPointQueryVo(resultMap, dataCode, startTime, endTime))
                .filter(Objects::nonNull)
                .collect(Collectors.toCollection(() ->
                        new ArrayList<>(dataCodes.size()))); // 预分配容量
    }

    /**
     * 使用普通流处理少量数据
     */
    private List<DataPointQueryVo> processWithStream(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            String startTime,
            String endTime,
            List<String> dataCodes) {

        return dataCodes.stream()
                .map(dataCode -> buildDataPointQueryVo(resultMap, dataCode, startTime, endTime))
                .filter(Objects::nonNull)
                .collect(Collectors.toCollection(() ->
                        new ArrayList<>(dataCodes.size()))); // 预分配容量
    }

    /**
     * 构建单个数据点位查询结果对象
     */
    private DataPointQueryVo buildDataPointQueryVo(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            String dataCode,
            String startTime,
            String endTime) {

        try {
            DataPointQueryVo vo = new DataPointQueryVo();
            vo.setDataCode(dataCode);
            vo.setTimestamp(startTime);

            BigDecimal propVal = findNearestValue(resultMap, dataCode, startTime, endTime);
            if (!ObjectUtil.isEmpty(propVal)) {
                // ROUND_HALF_UP 表示四舍五入
                vo.setPropVal(propVal.toString());
            }
            return vo;
        } catch (Exception e) {
            log.error("Error building DataPointQueryVo for dataCode: {}", dataCode, e);
            return null;
        }
    }

    /**
     * 查找最接近目标时间的值
     *
     * @param resultMap  数据映射
     * @param dataCode   数据点位编码
     * @param targetTime 目标时间
     * @param endTime    结束时间
     * @return 最接近的值
     */
    public BigDecimal findNearestValue(
            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
            String dataCode,
            String targetTime,
            String endTime) {

        try {
            ConcurrentHashMap<String, BigDecimal> tsValMap = resultMap.get(dataCode);
            if (tsValMap == null || tsValMap.isEmpty()) {
                log.debug("No data found for dataCode: {}", dataCode);
                return null;
            }
            LocalDateTime target = DateUtils.parseDateTime(targetTime);
            LocalDateTime end = DateUtils.parseDateTime(endTime);
            // 找到时间范围内最接近目标时间的值
            Map.Entry<String, BigDecimal> nearestEntry = tsValMap.entrySet().stream()
                    .filter(entry -> {
                        LocalDateTime timestamp = DateUtils.parseDateTime(entry.getKey());
                        return !timestamp.isBefore(target) && !timestamp.isAfter(end);
                    })
                    .min((e1, e2) -> {
                        LocalDateTime t1 = DateUtils.parseDateTime(e1.getKey());
                        LocalDateTime t2 = DateUtils.parseDateTime(e2.getKey());
                        long diff1 = Math.abs(ChronoUnit.SECONDS.between(target, t1));
                        long diff2 = Math.abs(ChronoUnit.SECONDS.between(target, t2));
                        return Long.compare(diff1, diff2);
                    })
                    .orElse(null);

            return nearestEntry != null ? nearestEntry.getValue() : null;

        } catch (Exception e) {
            log.error("Error finding nearest value for dataCode: {}, targetTime: {}",
                    dataCode, targetTime, e);
            return null;
        }
    }

    public void calDerivedIndicator(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, List<String> propList, TimeQueryDTO timeQueryDTO) {

        // 1. 获取所有的时间戳
        Set<String> allTimestamps = new HashSet<>();
        // 如果不是等时间间隔查询，只添加开始时间点
        if (!timeQueryDTO.isEquallySpacedQuery()) {
            allTimestamps.add(timeQueryDTO.getStartTime());
        } else {
            // 如果是等时间间隔查询，收集所有已计算结果中的时间点
            for (Map<String, BigDecimal> innerMap : resultMap.values()) {
                allTimestamps.addAll(innerMap.keySet());
            }
        }

        //声明list,存放衍生指标计算目标属性
        List<FormulaClass> formulaList = propList.stream()
                .filter(targetProp -> StringUtils.isNotBlank(ruleDetailInstanceService.getFormula(targetProp, ConstantBase.RULECOLID_COMMON)))
                .map(targetProp -> new FormulaClass(targetProp, ruleDetailInstanceService.getFormula(targetProp, ConstantBase.RULECOLID_COMMON)))
                .collect(Collectors.toList());
        //对计算公式进行排序
        List<String> orderFormulaDevProp = FormulaUtils.calculateOrder(formulaList);
        List<Callable<Void>> tasks = new ArrayList<>();
        for (String propCode : orderFormulaDevProp) {
            if (propList.contains(propCode)) {
                //需要计算
                String formula = ruleDetailInstanceService.getFormula(propCode, ConstantBase.RULECOLID_COMMON);
                if (StringUtils.isNotBlank(formula)) {
                    if (formula.contains(ConstantBase.YOY) || formula.contains(ConstantBase.MOM)) {
                        //如果是同比和环比，需要进行特殊处理,将计算结果放入resultMap中
                        calYoYAndMoM(formula, propCode, timeQueryDTO, resultMap, tasks);
                    } else {
                        if (formula.contains(ConstantBase.DURATION)) {
                            //如果包含时长，将时长计算出来放入formula中
                            String startTime = timeQueryDTO.getStartTime();
                            String endTime = timeQueryDTO.getEndTime();
                            int seconds = DateUtils.secondDiff(DateUtils.parseDate(endTime), DateUtils.parseDate(startTime));
                            formula = formula.replace(ConstantBase.DURATION, String.valueOf(seconds));
                        }
                        String formulaNew = formula;
                        List<String> varList = FormulaUtils.getVarList(formulaNew);

                        allTimestamps.forEach(timestamp -> {
                            Callable<Void> task = () -> {

                                Map<String, BigDecimal> calValueMap = new ConcurrentHashMap<>();
                                //计算结果，并将结果放入propValueMap中
                                for (String prop : varList) {
                                    //获取属性值
                                    BigDecimal propTsValue = null;
                                    if (ObjectUtil.isEmpty(timeQueryDTO.getInterval()) && StrUtil.isBlank(timeQueryDTO.getTsUnit())) {
                                        propTsValue = findNearestValue(resultMap, prop, timestamp, timeQueryDTO.getEndTime());
                                    } else {
                                        propTsValue = getPropTsValue(resultMap, prop, timestamp);
                                    }

                                    if (!ObjectUtils.isEmpty(propTsValue)) {
                                        calValueMap.put(prop, propTsValue);
                                    }
                                }
                                BigDecimal bigDecimal = FormulaUtils.calcFormula(formulaNew, calValueMap);
                                if (ObjectUtil.isNotEmpty(bigDecimal)) {
                                    resultMap.computeIfAbsent(propCode, k -> new ConcurrentHashMap<>())
                                            .put(timestamp, bigDecimal);
                                }

                                return null;
                            };
                            tasks.add(task);
                        });

                    }
                }
            }
        }
        // 提交所有任务到线程池
        List<Future<Void>> futures = tasks.stream()
                .map(ThreadPoolHolder.getExecutorService()::submit)
                .collect(Collectors.toList());
        // 等待所有任务完成
        for (Future<Void> future : futures) {
            try {
                future.get(); // 等待任务完成
            } catch (InterruptedException | ExecutionException e) {
                Thread.currentThread().interrupt();
                log.error("任务执行失败", e);
            }
        }

    }

    /**
     * @return void
     * <AUTHOR>
     * @Description //TODO 计算同比环比
     * @Date 9:49 2024/10/14
     * @Param [formula, propCode, queryDataByIntervalTimeDTO]
     **/
    private void calYoYAndMoM(String formula, String propCode, TimeQueryDTO queryDataByIntervalTimeDTO, ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, List<Callable<Void>> tasks) {
        Integer interval = ObjectUtil.isEmpty(queryDataByIntervalTimeDTO.getInterval()) ? 1 : queryDataByIntervalTimeDTO.getInterval();
        //获取formula的函数名称和计算属性
        TimeUnitDto timeUnitDto = new TimeUnitDto();
        timeUnitDto.setStartTime(queryDataByIntervalTimeDTO.getStartTime());
        timeUnitDto.setEndTime(queryDataByIntervalTimeDTO.getEndTime());
        timeUnitDto.setInterval(interval);
        timeUnitDto.setTimeUnit(TimeUnit.fromString(queryDataByIntervalTimeDTO.getTsUnit()));
        Matcher matcher = FUNCTION_PATTERN.matcher(formula);
        List<String> dataCodes = queryDataByIntervalTimeDTO.getDataCodes();
        HashMap<String, String> tsMap = new HashMap<>();
        if (matcher.matches()) {
            String functionName = matcher.group(1);
            String devProperty = matcher.group(2);
            TimeUnitDto basePeriod = RateUtils.getBasePeriod(timeUnitDto, functionName);
            List<String[]> timeGroups = generateTimeGroups(queryDataByIntervalTimeDTO.getStartTime(), queryDataByIntervalTimeDTO.getEndTime(),
                    queryDataByIntervalTimeDTO.getInterval(), queryDataByIntervalTimeDTO.getTsUnit());

            for (String[] timeGroup : timeGroups) {
                TimeUnitDto timeUnitDtoSub = new TimeUnitDto();
                timeUnitDtoSub.setStartTime(timeGroup[0]);
                timeUnitDtoSub.setEndTime(timeGroup[1]);
                timeUnitDtoSub.setInterval(queryDataByIntervalTimeDTO.getInterval());
                timeUnitDtoSub.setTimeUnit(TimeUnit.fromString(queryDataByIntervalTimeDTO.getTsUnit()));
                TimeUnitDto basePeriodSubBase = RateUtils.getBasePeriod(timeUnitDtoSub, functionName);
                //将其对应的数据存储到map中
                tsMap.put(basePeriodSubBase.getStartTime(), timeUnitDtoSub.getStartTime());
            }
            dataCodes.add(devProperty);
            //递归获取计算属性涉及到的所有属性编码
            queryDataByIntervalTimeDTO.setDataCodes(dataCodes);
            queryDataByIntervalTimeDTO.setStartTime(basePeriod.getStartTime());
            queryDataByIntervalTimeDTO.setEndTime(basePeriod.getEndTime());
            queryDataByIntervalTimeDTO.setTsUnit(basePeriod.getTimeUnit().getOriName());
            queryDataByIntervalTimeDTO.setInterval(basePeriod.getInterval());
            List<DataIntervalQueryVo> equallySpacedTimeData = getEquallySpacedTimeData(queryDataByIntervalTimeDTO);
            //将计算结果中的数据放入resultMap中
            for (DataIntervalQueryVo dataIntervalQueryVo : equallySpacedTimeData) {
                if (devProperty.equals(dataIntervalQueryVo.getDataCode())) {
                    HashMap<String, BigDecimal> map = new HashMap<>();
                    for (ValTimes valTime : dataIntervalQueryVo.getValTimes()) {
                        if (StrUtil.isNotBlank(tsMap.getOrDefault(valTime.getTimestamp(), null)) && StrUtil.isNotBlank(valTime.getPropVal())) {
                            map.put(tsMap.getOrDefault(valTime.getTimestamp(), null), new BigDecimal(valTime.getPropVal()));
                        }
                    }
                    resultMap.computeIfAbsent(devProperty + functionName, k -> new ConcurrentHashMap<>()).putAll(map);
                }
            }
            //计算比值
            tsMap.values().forEach(timestamp -> {
                Callable<Void> task = () -> {

                    //获取当期属性值
                    BigDecimal propTsValue = getPropTsValue(resultMap, devProperty, timestamp);
                    //获取基期属性值
                    BigDecimal basePeriodPropTsValue = getPropTsValue(resultMap, devProperty + functionName, timestamp);
                    BigDecimal result = null;
                    if (ObjectUtil.isNotEmpty(propTsValue) && ObjectUtil.isNotEmpty(basePeriodPropTsValue)) {
                        // 检查除数是否为零
                        if (basePeriodPropTsValue.signum() != 0) {
                            // 进行除法运算，并设置精度
                            result = propTsValue.subtract(basePeriodPropTsValue)
                                    .divide(basePeriodPropTsValue, 2, RoundingMode.HALF_UP)
                                    .multiply(new BigDecimal("100"));
                        } else {
                            // 处理除数为零的情况
                            // 可以选择返回特定值或抛出异常
                            // 这里返回 null 表示计算失败
                            result = null;
                        }
                    }
                    if (ObjectUtil.isNotEmpty(result)) {
                        resultMap.computeIfAbsent(propCode, k -> new ConcurrentHashMap<>())
                                .put(timestamp, result);
                    }

                    return null;
                };
                tasks.add(task);
            });
        }

    }


    /*
     * <AUTHOR>
     * @Description //获取属性值
     * @Date 10:37 2024/8/8
     * @Param
     * @return
     **/
    private BigDecimal getPropTsValue(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> map, String propCode, String ts) {
        Map<String, BigDecimal> innerMap = map.getOrDefault(propCode, null);
        if (!ObjectUtils.isEmpty(innerMap)) {
            return innerMap.getOrDefault(ts, null);
        }
        return null;
    }

    /*
     * <AUTHOR>
     * @Description 将设备属性列表按照项目编码分组
     * @Date 8:59 2024/8/5
     * @Param
     * @return
     **/
    public Map<String, List<String>> groupDevpropertyByprojectCode(List<String> devpropList) {
        Map<String, List<String>> groupedMap = new HashMap<>();

        for (String devproperty : devpropList) {

            //如果设备属性编码是自定义的，不是来自于数字孪生，需要获取计算公式中的属性编码，用来获取项目编码
            if (!com.siact.code.util.InsCodeUtil.isInsCode(devproperty)) {
                String formula = ruleDetailInstanceService.getFormula(devproperty, ConstantBase.RULECOLID_COMMON);
                if (StrUtil.isNotBlank(formula)) {
                    List<String> varList = FormulaUtils.getVarList(formula);
                    devproperty = varList.get(0);
                }
            }
            String projectCode = InsCodeUtil.getProjectDataCode(devproperty);
            // 如果map中没有这个项目编码的key，就创建一个新的列表
            groupedMap.computeIfAbsent(projectCode, k -> new ArrayList<>()).add(devproperty);
        }
        return groupedMap;
    }

    /*
     * <AUTHOR>
     * @Description //将设备属性列表按照计算类型分组,将采集属性/基础指标属性与聚合指标/衍生指标区分开，前者从数据库中查询，后者基于前者进行计算
     * @Date 9:07 2024/8/5
     * @Param
     * @return
     **/

    public Map<String, List<String>> groupDevpropertyByCalType(List<String> devpropList, Map<Integer, List<String>> calTypeMap) {
        Map<String, List<String>> groupedMap = new HashMap<>();
        //获取计算类型是2(聚合指标)和3(衍生指标)devpropertyList列表
        for (String propCode : devpropList) {
            List<String> aggList = calTypeMap.getOrDefault(ConstantBase.CAL_AGGREGATION_TYPE, new ArrayList<>());
            List<String> derivedList = calTypeMap.getOrDefault(ConstantBase.CAL_DERIVED_TYPE, new ArrayList<>());
            List<String> baseList = calTypeMap.getOrDefault(ConstantBase.CAL_BASE_TYPE, new ArrayList<>());
            if (aggList.contains(propCode)) {
                groupedMap.computeIfAbsent(ConstantBase.CAL_AGG, k -> new ArrayList<>()).add(propCode);
            } else if (derivedList.contains(propCode)) {
                groupedMap.computeIfAbsent(ConstantBase.CAL_DER, k -> new ArrayList<>()).add(propCode);
            } else if (baseList.contains(propCode)) {
                groupedMap.computeIfAbsent(ConstantBase.BASE_PROP, k -> new ArrayList<>()).add(propCode);
                groupedMap.computeIfAbsent(ConstantBase.ORI_BASE_PROP, k -> new ArrayList<>()).add(propCode);
            } else {
                groupedMap.computeIfAbsent(ConstantBase.ORI_BASE_PROP, k -> new ArrayList<>()).add(propCode);
            }

        }
        return groupedMap;
    }


    /**
     * 计算等时间间隔/时间区间的公式结果
     *
     * @param calculateFormulaDTO 计算参数
     * @return 计算结果列表
     */
    public List<DataIntervalQueryVo> calculateFormulaEquallySpacedTime(TimeQueryFormulaDTO calculateFormulaDTO) {
        log.info("开始计算等时间间隔公式, 参数: {}", calculateFormulaDTO);
        StopWatch stopWatch = new StopWatch("公式计算");

        try {
            stopWatch.start("参数验证和准备");
            // 验证参数
            validateCalculateParams(calculateFormulaDTO);

            // 准备计算数据
            List<String> devCodes = new ArrayList<>();
            List<CalFormula> calFormulaList = calculateFormulaDTO.getCalFormulaList();
            List<RuleDetailVo> ruleDetails = prepareCalculationRules(calFormulaList, devCodes);

            stopWatch.stop();
            stopWatch.start("执行计算");

            // 执行计算并获取结果
            List<DataIntervalQueryVo> result = executeCalculation(calculateFormulaDTO, devCodes, ruleDetails);

            stopWatch.stop();
            log.info("公式计算完成, 耗时统计: {}", stopWatch.prettyPrint());

            return result;

        } catch (Exception e) {
            log.error("公式计算失败: {}", e.getMessage(), e);
            throw new BizException("公式计算失败: " + e.getMessage());
        }
    }

    /**
     * 计算等时间间隔的公式结果
     *
     * @param dto 计算参数
     * @return 计算结果列表
     */
    public List<DataPointQueryVo> calculateFormulaTimeSlice(CalculateFormulaSliceTimeDTO dto) {
        log.info("开始计算时间截面公式, 参数: {}", dto);
        StopWatch stopWatch = new StopWatch("时间截面公式计算");

        try {
            stopWatch.start("参数验证和准备");
            // 验证参数,仅需验证计算公式列表是否为空即可
            if (CollectionUtils.isEmpty(dto.getCalFormulaList())) {
                throw new BizException("计算公式列表不能为空");
            }
            // 准备计算数据
            List<String> devCodes = new ArrayList<>();
            List<RuleDetailVo> ruleDetails = prepareCalculationRules(dto.getCalFormulaList(), devCodes);

            stopWatch.stop();
            stopWatch.start("执行计算");

            // 执行计算并获取结果
            List<DataPointQueryVo> result = executeFormulaCalculation(dto, devCodes, ruleDetails);

            stopWatch.stop();
            log.info("公式计算完成, 耗时统计: {}", stopWatch.prettyPrint());

            return result;

        } catch (Exception e) {
            log.error("公式计算失败: {}", e.getMessage(), e);
            throw new BizException("公式计算失败: " + e.getMessage());
        }
    }

    private List<DataPointQueryVo> executeFormulaCalculation(CalculateFormulaSliceTimeDTO dto, List<String> devCodes, List<RuleDetailVo> ruleDetails) {
        List<DataPointQueryVo> result = getTimeSliceData(buildQueryDataByPointTimeDTO(dto, devCodes));
        return result;
    }

    /**
     * 验证计算参数
     */
    private void validateCalculateParams(TimeQueryFormulaDTO dto) {
        if (CollectionUtils.isEmpty(dto.getCalFormulaList())) {
            throw new BizException("计算公式列表不能为空");
        }

        if (StringUtils.isBlank(dto.getStartTime()) || StringUtils.isBlank(dto.getEndTime())) {
            throw new BizException("开始时间和结束时间不能为空");
        }

        // 验证时间格式
        if (!DateUtils.isTimeFormatValid(dto.getStartTime(), DateUtils.YMD_HMS) ||
                !DateUtils.isTimeFormatValid(dto.getEndTime(), DateUtils.YMD_HMS)) {
            throw new BizException("时间格式不正确，应为: yyyy-MM-dd HH:mm:ss");
        }
        if (dto.isEquallySpacedQuery()) {
            TimeQueryDTO timeQueryDTO = new TimeQueryDTO();
            timeQueryDTO.setInterval(dto.getInterval());
            timeQueryDTO.setTsUnit(dto.getTsUnit());
            //验证时间间隔和时间间隔单位
            validateEquallySpacedParameters(timeQueryDTO);
        }

    }


    /**
     * 准备计算规则
     */
    private List<RuleDetailVo> prepareCalculationRules(List<CalFormula> calFormulaList, List<String> devCodes) {
        List<RuleDetailVo> ruleDetails = new ArrayList<>();

        for (CalFormula calFormula : calFormulaList) {
            RuleDetailVo ruleDetail = createRuleDetail(calFormula, devCodes);
            ruleDetails.add(ruleDetail);

            // 如果有聚合类型，添加额外的规则
            if (StringUtils.isNotBlank(calFormula.getAggType())) {
                RuleDetailVo aggRuleDetail = createAggregateRuleDetail(calFormula);
                ruleDetails.add(aggRuleDetail);
            }
        }
        return ruleDetails;
    }


    /**
     * 创建基础规则详情
     */
    private RuleDetailVo createRuleDetail(CalFormula calFormula, List<String> devCodes) {
        RuleDetailVo vo = new RuleDetailVo();
        vo.setRuleFormula(calFormula.getFormula());
        vo.setCalType(CalTypeEnum.BASE.getValue());
        vo.setRuleType(RuleTypeEnum.INS.getValue());
        vo.setRuleColId(ConstantBase.RULECOLID_COMMON);

        // 设置项目编码
        List<String> varList = FormulaUtils.getVarList(calFormula.getFormula());
        if (!CollectionUtils.isEmpty(varList)) {
            String projectCode = InsCodeUtil.getProjectDataCode(varList.get(0));
            vo.setProjectCode(projectCode);
        }

        // 设置目标属性
        String targetProp = calFormula.getTargetProp();
        if (StringUtils.isNotBlank(calFormula.getAggType())) {
            vo.setDevProperty(targetProp + calFormula.getAggType());
            devCodes.add(targetProp + calFormula.getAggType());
            devCodes.add(targetProp);
        } else {
            vo.setDevProperty(targetProp);
            devCodes.add(targetProp);
        }

        return vo;
    }


    /**
     * 创建聚合规则详情
     */
    private RuleDetailVo createAggregateRuleDetail(CalFormula calFormula) {
        RuleDetailVo vo = new RuleDetailVo();
        String aggFormula = String.format("%s(@[%s%s])",
                calFormula.getAggType(),
                calFormula.getTargetProp(),
                calFormula.getAggType());

        vo.setRuleFormula(aggFormula);
        vo.setCalType(CalTypeEnum.AGGREGATE.getValue());
        vo.setDevProperty(calFormula.getTargetProp());
        vo.setRuleType(RuleTypeEnum.INS.getValue());
        vo.setRuleColId(ConstantBase.RULECOLID_COMMON);

        return vo;
    }


    /**
     * 执行计算并获取结果
     */
    private List<DataIntervalQueryVo> executeCalculation(
            TimeQueryFormulaDTO dto,
            List<String> devCodes,
            List<RuleDetailVo> ruleDetails) {

        try {

            // 获取计算结果
            List<DataIntervalQueryVo> result;
            if (dto.isEquallySpacedQuery()) {
                result = getEquallySpacedTimeData(buildEquallySpacedQuery(dto, devCodes));
            } else {
                result = convertDataPointsToIntervals(
                        getTimeIntervalData(buildTimeIntervalQuery(dto, devCodes))
                );
            }

            return result;

        } finally {
        }
    }


    /**
     * 构建间隔查询参数
     */
    private TimeQueryDTO buildEquallySpacedQuery(
            TimeQueryFormulaDTO dto,
            List<String> devCodes) {
        TimeQueryDTO queryDTO = new TimeQueryDTO();
        BeanUtils.copyProperties(dto, queryDTO);
        queryDTO.setDataCodes(devCodes);
        return queryDTO;
    }

    /**
     * 构建时间区间查询参数
     */
    private TimeQueryDTO buildTimeIntervalQuery(
            TimeQueryFormulaDTO dto,
            List<String> devCodes) {
        TimeQueryDTO queryDTO = new TimeQueryDTO();
        queryDTO.setStartTime(dto.getStartTime());
        queryDTO.setEndTime(dto.getEndTime());
        queryDTO.setDataCodes(devCodes);
        return queryDTO;
    }

    /**
     * 构建时间点查询参数
     */
    private QueryDataByPointTimeDTO buildQueryDataByPointTimeDTO(
            CalculateFormulaSliceTimeDTO dto,
            List<String> devCodes) {
        QueryDataByPointTimeDTO queryDTO = new QueryDataByPointTimeDTO();
        queryDTO.setTs(dto.getTs());
        queryDTO.setDataCodes(devCodes);
        return queryDTO;
    }

    public static List<DataIntervalQueryVo> convertDataPointsToIntervals(List<DataPointQueryVo> dataPoints) {
        // 使用Map来分组DataPointQueryVo对象
        Map<String, List<DataPointQueryVo>> groupedDataPoints = new HashMap<>();
        for (DataPointQueryVo dataPoint : dataPoints) {
            String key = dataPoint.getDataCode() + "&" + dataPoint.getPropVal();
            groupedDataPoints.computeIfAbsent(key, k -> new ArrayList<>()).add(dataPoint);
        }

        // 将分组后的数据转换为List<DataIntervalQueryVo>
        List<DataIntervalQueryVo> intervals = new ArrayList<>();
        for (Map.Entry<String, List<DataPointQueryVo>> entry : groupedDataPoints.entrySet()) {
            DataIntervalQueryVo interval = new DataIntervalQueryVo();
            interval.setDataCode(entry.getKey().split("&")[0]);
            List<ValTimes> valTimesList = new ArrayList<>();
            for (DataPointQueryVo dataPoint : entry.getValue()) {
                ValTimes valTimes = new ValTimes();
                valTimes.setPropVal(dataPoint.getPropVal());
                valTimes.setTimestamp(dataPoint.getTimestamp());
                valTimesList.add(valTimes);
            }
            interval.setValTimes(valTimesList);
            intervals.add(interval);
        }

        return intervals;
    }


    public void queryTimeData(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, TimeQueryDTO queryDTO) {
        //复制一个queryDTO
        TimeQueryDTO queryDTOCopy = new TimeQueryDTO();
        BeanUtils.copyProperties(queryDTO, queryDTOCopy);
        // 1. 参数验证
        validateTimeQuery(queryDTOCopy);

        StopWatch stopWatch = new StopWatch();
        stopWatch.start("开始获取时间序列数据");
        // 2. 准备计算环境
        Long ruleColID = ConstantBase.RULECOLID_COMMON;
        //转化时间格式
        queryDTOCopy.setTsUnit(convertTsUnit(queryDTOCopy.getTsUnit()));
        try {
            // 3. 获取计算所需的所有变量
            List<String> dataCodes = queryDTOCopy.getDataCodes();
            List<String> formulaVarList = getFormulaVarList(dataCodes);

            // 4. 按项目分组并处理
            Map<String, List<String>> projectMap = groupDevpropertyByprojectCode(formulaVarList);
            projectMap.entrySet().forEach(entry -> {
                processProjectData(entry.getKey(), entry.getValue(), resultMap, queryDTOCopy, ruleColID);
            });

            stopWatch.stop();
            log.info(stopWatch.prettyPrint());
        } catch (Exception e) {
            log.error("处理时间序列数据失败", e);
            throw new BizException("处理时间序列数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取公式中涉及的所有变量（包含递归依赖）
     */
    public List<String> getFormulaVarList(List<String> targetPropList) {
        if (CollUtil.isEmpty(targetPropList)) {
            return Collections.emptyList();
        }

        Set<String> resultSet = Collections.synchronizedSet(new LinkedHashSet<>());
        Set<String> processed = Collections.synchronizedSet(new HashSet<>());

        try {
            StopWatch stopWatch = new StopWatch("获取公式变量");
            stopWatch.start("处理变量依赖");

            // 处理变量依赖关系，直接使用RuleDetailInstanceService
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                targetPropList.parallelStream().forEach(prop ->
                        processVariableDependencies(prop, resultSet, processed, 0));
            }, executor);

            // 设置超时
            try {
                future.get(5, java.util.concurrent.TimeUnit.MINUTES);
            } catch (TimeoutException e) {
                log.error("处理变量依赖超时", e);
                future.cancel(true);
            }

            stopWatch.stop();
            log.info("获取公式变量完成，目标属性数: {}, 总变量数: {}, 耗时统计:\n{}",
                    targetPropList.size(), resultSet.size(), stopWatch.prettyPrint());

            return new ArrayList<>(resultSet);

        } catch (Exception e) {
            log.error("获取公式变量列表失败: {}", e.getMessage(), e);
            return new ArrayList<>(targetPropList);  // 降级处理：直接返回目标属性列表
        }
    }

    /**
     * 递归处理变量依赖
     */
    private void processVariableDependencies(String propCode, Set<String> resultSet,
                                             Set<String> processed, int depth) {
        // 防止递归过深
        if (depth > 100) {
            log.warn("变量依赖递归深度超过限制: {}", propCode);
            return;
        }

        if (!processed.add(propCode)) {
            return;
        }

        resultSet.add(propCode);

        // 直接从RuleDetailInstanceService获取公式
        String formula = ruleDetailInstanceService.getFormula(propCode, ConstantBase.RULECOLID_COMMON);

        if (StrUtil.isNotBlank(formula)) {
            List<String> variables = FormulaUtils.getVarList(formula);
            if (CollUtil.isNotEmpty(variables)) {
                for (String var : variables) {
                    if (!processed.contains(var)) {
                        resultSet.add(var);

                        // 递归处理变量依赖
                        processVariableDependencies(var, resultSet, processed, depth + 1);
                    }
                }
            }
        }
    }


    private List<DataPointQueryVo> returnIntervalResult(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, TimeQueryDTO queryDTO) {
        List<String> dataCodes = queryDTO.getDataCodes();
        String startTime = queryDTO.getStartTime();
        String endTime = queryDTO.getEndTime();
        // 参数校验
        if (CollectionUtils.isEmpty(dataCodes)) {
            return Collections.emptyList();
        }

        // 根据数据量决定是否使用并行流
        if (dataCodes.size() > 100) {
            return processWithParallelStream(resultMap, startTime, endTime, dataCodes);
        } else {
            return processWithStream(resultMap, startTime, endTime, dataCodes);
        }
    }

    /**
     * 处理单个项目的数据
     *
     * @param projectCode   项目编码
     * @param devProperties 设备属性列表
     * @param resultMap     结果Map
     * @param queryDTO      查询参数
     * @param ruleColID     规则列ID
     */
    private void processProjectData(String projectCode,
                                    List<String> devProperties,
                                    ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                    TimeQueryDTO queryDTO,
                                    Long ruleColID) {
        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("处理项目数据: " + projectCode);

            // 1. 获取数据源配置
            DataSourceVo dataSourceVo = dataSourceService.getDataSourceConfig(projectCode);
            if (ObjectUtils.isEmpty(dataSourceVo)) {
                log.error("未找到项目{}的数据源配置", projectCode);
                return;
            }

            // 2. 获取计算类型配置
            Map<Integer, List<String>> calTypeConfigMap = ruleDetailInstanceService.getCalTypeConfigMap(projectCode, ruleColID);
            // 3. 按计算类型分组属性
            Map<String, List<String>> calTypePropMap = groupDevpropertyByCalType(devProperties, calTypeConfigMap);

            // 4. 获取时间分组
            List<String[]> timeGroups = new ArrayList<>();
            if (!queryDTO.isEquallySpacedQuery()) {
                //区间计算
                timeGroups.add(new String[]{queryDTO.getStartTime(), queryDTO.getEndTime()});
            } else {
                //等时间间隔取样计算
                timeGroups = TimeUtils.generateTimeGroups(queryDTO.getStartTime(), queryDTO.getEndTime(),
                        queryDTO.getInterval(), queryDTO.getTsUnit());
            }
            // 5. 处理基础指标
            processBaseIndicators(resultMap, calTypePropMap, queryDTO, dataSourceVo, timeGroups, ConstantBase.EQUALLY_INTERVAL_CAL);

            // 6. 处理聚合指标
            processAggregateIndicators(resultMap, calTypePropMap, queryDTO, dataSourceVo, timeGroups);

            // 7. 处理衍生指标
            processDerivedIndicators(resultMap, calTypePropMap, queryDTO);

            stopWatch.stop();
            log.info("项目{}数据处理完成, 耗时: {}ms", projectCode, stopWatch.getTotalTimeMillis());

        } catch (Exception e) {
            log.error("处理项目{}数据失败", projectCode, e);
        }
    }

    public void processDerivedIndicators(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, Map<String, List<String>> calTypePropMap, TimeQueryDTO queryDTO) {
        List<String> derivedIndicators = calTypePropMap.getOrDefault(ConstantBase.CAL_DER, null);
        if (ObjectUtil.isNotEmpty(derivedIndicators)) {
            calDerivedIndicator(resultMap, derivedIndicators, queryDTO);
        }
    }

    private List<DataIntervalQueryVo> returnEquallySpacedResult(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap, TimeQueryDTO queryDataByIntervalTimeDTO) {
        ArrayList<DataIntervalQueryVo> dataIntervalQueryVos = new ArrayList<>();
        //对时间按照等时间间隔进行分组
        List<String[]> timeGroups = TimeUtils.generateTimeGroups(queryDataByIntervalTimeDTO.getStartTime(), queryDataByIntervalTimeDTO.getEndTime(), queryDataByIntervalTimeDTO.getInterval(), queryDataByIntervalTimeDTO.getTsUnit());
        for (String propCode : queryDataByIntervalTimeDTO.getDataCodes()) {
            DataIntervalQueryVo dataIntervalQueryVo = new DataIntervalQueryVo();
            Map<String, BigDecimal> tsVals = resultMap.getOrDefault(propCode, null);
            dataIntervalQueryVo.setDataCode(propCode);
            ArrayList<ValTimes> tsValList = new ArrayList<>();
            if (!ObjectUtils.isEmpty(tsVals)) {
                for (String[] timeGroup : timeGroups) {
                    String time = timeGroup[0];
                    BigDecimal tsValue = tsVals.getOrDefault(time, null);
                    ValTimes valTimes = new ValTimes();
                    valTimes.setTimestamp(time);
                    if (ObjectUtils.isEmpty(tsValue)) {
                        valTimes.setPropVal(null);
                    } else {
                        valTimes.setPropVal(tsValue.toString());
                    }
                    tsValList.add(valTimes);
                }
            } else {
                for (String[] timeGroup : timeGroups) {
                    String time = timeGroup[0];
                    ValTimes valTimes = new ValTimes();
                    valTimes.setTimestamp(time);
                    valTimes.setPropVal(null);
                    tsValList.add(valTimes);
                }
            }
            // 排序
            Collections.sort(tsValList, Comparator.comparing(ValTimes::getTimestamp));
            dataIntervalQueryVo.setValTimes(tsValList);
            dataIntervalQueryVos.add(dataIntervalQueryVo);
        }
        return dataIntervalQueryVos;
    }

    private void validateTimeQuery(TimeQueryDTO queryDTO) {
        // 1. 基础参数验证
        validateBasicParameters(queryDTO);
        // 2. 时间格式和范围验证
        validateTimeParameters(queryDTO);
        // 3. 等时间间隔查询的特殊验证
        if (queryDTO.isEquallySpacedQuery()) {
            validateEquallySpacedParameters(queryDTO);
        }
    }

    private void validateBasicParameters(TimeQueryDTO queryDTO) {
        // 验证数据编码列表
        if (CollectionUtils.isEmpty(queryDTO.getDataCodes())) {
            throw new BizException("查询属性不能为空");
        }

        // 验证时间参数存在性
        if (StrUtil.isBlank(queryDTO.getStartTime())
                || StrUtil.isBlank(queryDTO.getEndTime())) {
            throw new BizException("开始时间和结束时间不能为空");
        }
    }

    private void validateTimeParameters(TimeQueryDTO queryDTO) {
        String startTime = queryDTO.getStartTime();
        String endTime = queryDTO.getEndTime();

        // 验证时间格式
        if (!DateUtils.isTimeFormatValid(startTime, DateUtils.YMD_HMS)) {
            throw new BizException("开始时间格式不正确，应为yyyy-MM-dd HH:mm:ss格式");
        }

        if (!DateUtils.isTimeFormatValid(endTime, DateUtils.YMD_HMS)) {
            throw new BizException("结束时间格式不正确，应为yyyy-MM-dd HH:mm:ss格式");
        }

        // 验证时间范围
        if (startTime.compareTo(endTime) > 0) {
            throw new BizException("开始时间不能大于结束时间");
        }
    }

    private void validateEquallySpacedParameters(TimeQueryDTO queryDTO) {
        Integer interval = queryDTO.getInterval();
        String tsUnit = queryDTO.getTsUnit();

        // 验证步长和单位的存在性
        if (interval == null || StrUtil.isBlank(tsUnit)) {
            throw new BizException("步长和步长单位必须同时设置");
        }

        // 验证步长值
        if (interval <= 0) {
            throw new BizException("步长必须大于0");
        }

        // 验证并转换时间单位
        String tsUnitConverted = convertTsUnit(tsUnit);
        if (StrUtil.isBlank(tsUnitConverted)) {
            throw new BizException("步长单位不正确，支持的单位有:(Y:年;M:月;D:日;H:小时;MIN:分)");
        }
    }


    /**
     * 处理基础指标
     *
     * @param resultMap      结果Map
     * @param calTypePropMap 计算类型属性Map
     * @param queryDTO       查询参数
     * @param dataSourceVo   数据源配置
     * @param flag           查询类型标志(0:等时间间隔, 1:时间区间, 2:时间点)
     */
    public void processBaseIndicators(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                      Map<String, List<String>> calTypePropMap,
                                      TimeQueryDTO queryDTO,
                                      DataSourceVo dataSourceVo,
                                      List<String[]> timeGroups,
                                      String flag) {
        log.info("处理基础指标...");
        try {
            // 1. 处理原始基础指标(ORI_BASE_PROP)
            List<String> oriBaseProps = calTypePropMap.getOrDefault(ConstantBase.ORI_BASE_PROP, null);
            if (!CollectionUtils.isEmpty(oriBaseProps)) {
                queryDTO.setDataCodes(null);
                switch (flag) {
                    case ConstantBase.EQUALLY_INTERVAL_CAL:
                        // 等时间间隔采样查询或时间间隔查询
                        dataBaseService.queryDataByEquallySpacedTime(resultMap, dataSourceVo, queryDTO, oriBaseProps);
                        break;
                    case ConstantBase.POINT_CAL:
                        // 时间点查询
                        dataBaseService.queryTimeSliceData(resultMap, dataSourceVo, queryDTO, oriBaseProps);
                        //将resultMap中的时间替换成为queryDTO中的时间
                        resultMap.forEach((k, v) -> {
                            v.forEach((time, value) -> {
                                v.remove(time);
                                v.put(queryDTO.getStartTime(), value);
                            });
                        });
                        timeGroups.add(new String[]{queryDTO.getStartTime(), queryDTO.getEndTime()});
                        break;
                    default:
                        throw new BizException("不支持的查询类型: " + flag);
                }
            }

            // 2. 处理静态属性数据及供热面积数据
            if (!CollectionUtils.isEmpty(oriBaseProps)) {
                List<String> staticPropertyList = oriBaseProps.stream()
                        .filter(item -> "1".equals(InsCodeUtil.getPropClassify(item).getCode())
                                || "GJM".equals(com.siact.code.util.InsCodeUtil.getModelCodeByDataCode(item)))
                        .collect(Collectors.toList());

                if (!staticPropertyList.isEmpty()) {
                    getStaticPropertyData(resultMap, staticPropertyList, timeGroups);
                }
            }

            // 3. 处理计算型基础指标(BASE_PROP)
            List<String> baseProps = calTypePropMap.getOrDefault(ConstantBase.BASE_PROP, null);
            if (!CollectionUtils.isEmpty(baseProps)) {
                // 获取需要计算的属性和对应的时间点
                Map<String, Set<String>> needCalcPropsWithTime = filterNeedCalculateProps(baseProps, resultMap, timeGroups);

                if (!needCalcPropsWithTime.isEmpty()) {
                    // 按时间点分组进行批量计算
                    Map<String, List<String>> timePointGroups = groupTimePointsForBatchCalculation(needCalcPropsWithTime);

                    // 对每组时间点进行并行计算
                    CompletableFuture<?>[] futures = timePointGroups.entrySet().stream()
                            .map(entry -> CompletableFuture.runAsync(() -> {
                                String timePoint = entry.getKey();
                                List<String> props = entry.getValue();

                                TimeQueryDTO batchQueryDTO = new TimeQueryDTO();
                                BeanUtils.copyProperties(queryDTO, batchQueryDTO);
                                batchQueryDTO.setStartTime(timePoint);
                                batchQueryDTO.setDataCodes(props);

                                // 执行计算
                                calBaseIndicator(resultMap, dataSourceVo, batchQueryDTO, flag,
                                        Collections.singletonList(new String[]{timePoint, timePoint}));
                            }, executor))
                            .toArray(CompletableFuture[]::new);

                    // 等待所有计算完成
                    CompletableFuture.allOf(futures).join();
                }
            }
        } catch (Exception e) {
            log.error("处理基础指标失败", e);
            throw new BizException("处理基础指标失败: " + e.getMessage());
        }
    }

    /**
     * 将时间点按照批次分组，优化计算性能
     */
    private Map<String, List<String>> groupTimePointsForBatchCalculation(Map<String, Set<String>> propTimePointsMap) {
        Map<String, List<String>> timePointGroups = new HashMap<>();

        // 按时间点分组，将相同时间点的属性放在一起计算
        propTimePointsMap.forEach((prop, timePoints) -> {
            timePoints.forEach(timePoint -> {
                timePointGroups.computeIfAbsent(timePoint, k -> new ArrayList<>()).add(prop);
            });
        });

        return timePointGroups;
    }

    /**
     * 过滤需要计算的属性和时间点
     *
     * @param props      基础计算指标列表
     * @param resultMap  已有结果Map
     * @param timeGroups 时间分组列表
     * @return 需要计算的属性和时间点的映射
     */
    private Map<String, Set<String>> filterNeedCalculateProps(List<String> props,
                                                              ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                                              List<String[]> timeGroups) {
        if (CollectionUtils.isEmpty(props)) {
            return new HashMap<>();
        }

        // 使用Map存储每个属性需要计算的时间点
        Map<String, Set<String>> propTimePointsMap = new HashMap<>();

        props.forEach(prop -> {
            ConcurrentHashMap<String, BigDecimal> timeValueMap = resultMap.get(prop);
            Set<String> missingTimePoints = new HashSet<>();

            // 检查每个时间点
            for (String[] timeGroup : timeGroups) {
                String timePoint = timeGroup[0];
                // 如果时间点不存在或值为null，则需要计算
                if (timeValueMap == null ||
                        !timeValueMap.containsKey(timePoint) ||
                        timeValueMap.get(timePoint) == null) {
                    missingTimePoints.add(timePoint);
                }
            }

            // 只有存在缺失的时间点才添加到计算列表
            if (!missingTimePoints.isEmpty()) {
                propTimePointsMap.put(prop, missingTimePoints);
            }
        });

        return propTimePointsMap;
    }

    /**
     * 处理聚合指标
     */
    public void processAggregateIndicators(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                           Map<String, List<String>> calTypePropMap,
                                           TimeQueryDTO queryDTO,
                                           DataSourceVo dataSourceVo,
                                           List<String[]> timeGroups) {
        StopWatch stopWatch = new StopWatch("处理聚合指标");
        try {
            stopWatch.start("获取聚合指标");
            // 1. 获取聚合指标列表
            List<String> aggIndicators = calTypePropMap.getOrDefault(ConstantBase.CAL_AGG, null);
            if (CollectionUtils.isEmpty(aggIndicators)) {
                return;
            }
            stopWatch.stop();

            // 2. 按聚合函数类型分组处理
            stopWatch.start("解析聚合函数");
            Map<String, Map<String, String>> aggTypeMap = parseAggregateFormulas(aggIndicators, calTypePropMap, resultMap, queryDTO, dataSourceVo, timeGroups);
            stopWatch.stop();

            // 3. 执行聚合计算
            stopWatch.start("执行聚合计算");
            executeAggregateCalculations(resultMap, aggTypeMap, dataSourceVo, queryDTO);
            stopWatch.stop();

            log.info("聚合指标处理完成: {}", stopWatch.prettyPrint());
        } catch (Exception e) {
            log.error("处理聚合指标失败: {}", e.getMessage(), e);
            throw new BizException("处理聚合指标失败: " + e.getMessage());
        }
    }

    /**
     * 执行聚合计算
     *
     * @param resultMap    结果Map
     * @param aggTypeMap   聚合类型映射
     * @param dataSourceVo 数据源配置
     * @param queryDTO     查询参数
     */
    private void executeAggregateCalculations(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                              Map<String, Map<String, String>> aggTypeMap,
                                              DataSourceVo dataSourceVo,
                                              TimeQueryDTO queryDTO) {
        try {
            // 1. 处理不同的聚合类型
            for (Map.Entry<String, Map<String, String>> entry : aggTypeMap.entrySet()) {
                String functionName = entry.getKey().toLowerCase();
                Map<String, String> propMapping = entry.getValue();

                // 2. 根据查询类型选择处理方式
                if (queryDTO.isEquallySpacedQuery()) {
                    // 等时间间隔采样聚合
                    processEquallyIntervalTimeAggregation(resultMap, functionName, propMapping, dataSourceVo, queryDTO);
                } else {
                    // 时间区间聚合
                    processIntervalTimeAggregation(resultMap, functionName, propMapping, dataSourceVo, queryDTO);
                }
            }
        } catch (Exception e) {
            log.error("执行聚合计算失败: {}", e.getMessage(), e);
            throw new BizException("执行聚合计算失败", e);
        }
    }

    /**
     * 处理等时间间隔采样聚合
     */
    private void processEquallyIntervalTimeAggregation(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                                       String functionName,
                                                       Map<String, String> propMapping,
                                                       DataSourceVo dataSourceVo,
                                                       TimeQueryDTO queryDTO) {
        if ("diff".equals(functionName)) {
            // 差值计算的特殊处理
            dataBaseService.processDiffQuery(resultMap, propMapping, queryDTO, dataSourceVo);
        } else {
            // 其他聚合函数处理
            dataBaseService.processAggQuery(resultMap, functionName, propMapping, queryDTO, dataSourceVo);
        }
    }


    /**
     * 处理时间区间聚合
     */
    private void processIntervalTimeAggregation(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                                String functionName,
                                                Map<String, String> propMapping,
                                                DataSourceVo dataSourceVo,
                                                TimeQueryDTO queryDTO) {
        if ("diff".equals(functionName)) {
            // 差值计算
            dataBaseService.processDiffQuery(resultMap, propMapping, queryDTO, dataSourceVo);
        } else {
            // 其他聚合函数
            dataBaseService.processRangeAggQuery(resultMap, functionName, propMapping, queryDTO, dataSourceVo);
        }

    }


    /**
     * 解析聚合函数公式
     */
    private Map<String, Map<String, String>> parseAggregateFormulas(List<String> aggIndicators,
                                                                    Map<String, List<String>> calTypePropMap,
                                                                    ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                                                    TimeQueryDTO queryDTO,
                                                                    DataSourceVo dataSourceVo,
                                                                    List<String[]> timeGroups) {
        Map<String, Map<String, String>> aggTypeMap = new HashMap<>();
        for (String targetProp : aggIndicators) {
            // 从内存缓存获取公式
            String formula = ruleDetailInstanceService.getFormula(targetProp,ConstantBase.RULECOLID_COMMON);
            if (StringUtils.isBlank(formula)) {
                log.warn("属性{}的计算公式未找到", targetProp);
                continue;
            }

            // 解析聚合函数
            Matcher matcher = FUNCTION_PATTERN.matcher(formula);
            if (!matcher.matches()) {
                log.warn("属性{}的公式格式不正确: {}", targetProp, formula);
                continue;
            }

            String functionName = matcher.group(1).toLowerCase();
            String sourceProperty = matcher.group(2);

            // 添加到聚合类型映射
            aggTypeMap.computeIfAbsent(functionName, k -> new HashMap<>())
                    .put(sourceProperty, targetProp);
        }
        return aggTypeMap;
    }


    /**
     * 批量处理基础指标计算
     */
    private void batchProcessBaseIndicators(Set<String> baseProps,
                                            Map<String, List<String>> calTypePropMap,
                                            ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                            TimeQueryDTO queryDTO,
                                            DataSourceVo dataSourceVo,
                                            List<String[]> timeGroups) {
        // 创建新的查询DTO，避免修改原始对象
        TimeQueryDTO batchQueryDTO = new TimeQueryDTO();
        BeanUtils.copyProperties(queryDTO, batchQueryDTO);
        batchQueryDTO.setDataCodes(new ArrayList<>(baseProps));

        // 按时间段分批处理
        int batchSize = 20; // 可配置的批次大小
        List<List<String[]>> timeGroupBatches = Lists.partition(timeGroups, batchSize);

        // 并行处理每个时间批次
        CompletableFuture<?>[] futures = timeGroupBatches.stream()
                .map(timeGroupBatch -> CompletableFuture.runAsync(() -> {
                    try {
                        // 为每个批次创建临时结果Map
                        ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> batchResultMap =
                                new ConcurrentHashMap<>();
                        batchQueryDTO.setStartTime(timeGroupBatch.get(0)[0]);
                        batchQueryDTO.setEndTime(timeGroupBatch.get(timeGroupBatch.size() - 1)[1]);
                        batchQueryDTO.setInterval(1);
                        batchQueryDTO.setTsUnit("m");
                        // 执行基础指标计算
                        processBaseIndicatorsForAgg(batchResultMap, calTypePropMap, batchQueryDTO, dataSourceVo, ConstantBase.EQUALLY_INTERVAL_CAL);
                    } catch (Exception e) {
                        log.error("批量处理基础指标失败: {}", e.getMessage(), e);
                        throw new CompletionException(e);
                    }
                }, executor))
                .toArray(CompletableFuture[]::new);

        // 等待所有批次处理完成
        try {
            CompletableFuture.allOf(futures).join();
        } catch (CompletionException e) {
            throw new BizException("批量处理基础指标失败: " + e.getMessage());
        }
    }

    private void processBaseIndicatorsForAgg(ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                             Map<String, List<String>> calTypePropMap,
                                             TimeQueryDTO queryDTO,
                                             DataSourceVo dataSourceVo,
                                             String flag) {
        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("处理聚合计算中的基础指标数据");

            // 4. 获取时间分组
            List<String[]> timeGroups = new ArrayList<>();
            if (!queryDTO.isEquallySpacedQuery()) {
                //区间计算
                timeGroups.add(new String[]{queryDTO.getStartTime(), queryDTO.getEndTime()});
            } else {
                //等时间间隔取样计算
                timeGroups = TimeUtils.generateTimeGroups(queryDTO.getStartTime(), queryDTO.getEndTime(),
                        queryDTO.getInterval(), queryDTO.getTsUnit());
            }
            Map<String, List<String>> needCalBaseListMap = filterBaseIndicatorsByDevCode(calTypePropMap, queryDTO);
            // 5. 处理基础指标
            processBaseIndicators(resultMap, needCalBaseListMap, queryDTO, dataSourceVo, timeGroups, ConstantBase.EQUALLY_INTERVAL_CAL);

            stopWatch.stop();
            log.info("聚合计算中基础指标数据处理完成, 耗时: {}ms", stopWatch.getTotalTimeMillis());

        } catch (Exception e) {
            log.error("聚合计算中基础指标数据失败", e);
        }
    }

    /**
     * 根据设备编码筛选基础指标
     *
     * @param calTypePropMap 原始指标映射
     * @param queryDTO       查询参数
     * @return 筛选后的指标映射
     */
    private Map<String, List<String>> filterBaseIndicatorsByDevCode(Map<String, List<String>> calTypePropMap,
                                                                    TimeQueryDTO queryDTO) {
        // 1. 参数校验
        if (CollectionUtil.isEmpty(calTypePropMap) ||
                CollectionUtils.isEmpty(queryDTO.getDataCodes())) {
            return Collections.emptyMap();
        }

        try {
            Map<String, List<String>> filteredMap = new HashMap<>();
            List<String> devCodes = queryDTO.getDataCodes();

            // 2. 处理 ORI_BASE
            List<String> oriBaseProps = calTypePropMap.getOrDefault(ConstantBase.ORI_BASE_PROP, Collections.emptyList());
            if (!CollectionUtils.isEmpty(oriBaseProps)) {
                List<String> filteredOriBase = oriBaseProps.stream()
                        .filter(prop -> devCodes.stream()
                                .anyMatch(devCode -> prop.startsWith(devCode)))
                        .collect(Collectors.toList());

                if (!filteredOriBase.isEmpty()) {
                    filteredMap.put(ConstantBase.ORI_BASE_PROP, filteredOriBase);
                }
            }

            // 3. 处理 BASE_PROP
            List<String> baseProps = calTypePropMap.getOrDefault(ConstantBase.BASE_PROP, Collections.emptyList());
            if (!CollectionUtils.isEmpty(baseProps)) {
                List<String> filteredBase = baseProps.stream()
                        .filter(prop -> devCodes.stream()
                                .anyMatch(devCode -> prop.startsWith(devCode)))
                        .collect(Collectors.toList());

                if (!filteredBase.isEmpty()) {
                    filteredMap.put(ConstantBase.BASE_PROP, filteredBase);
                }
            }


            log.debug("指标筛选结果: 原始ORI_BASE数量={}, 筛选后={}, 原始BASE数量={}, 筛选后={}",
                    oriBaseProps.size(),
                    filteredMap.getOrDefault(ConstantBase.ORI_BASE_PROP, Collections.emptyList()).size(),
                    baseProps.size(),
                    filteredMap.getOrDefault(ConstantBase.BASE_PROP, Collections.emptyList()).size());

            return filteredMap;

        } catch (Exception e) {
            log.error("筛选基础指标失败: {}", e.getMessage(), e);
            return new HashMap<>(calTypePropMap); // 发生错误时返回原始map
        }
    }

    /**
     * 判断是否需要重新计算基础指标
     *
     * @param sourceProperty 源属性
     * @param calTypePropMap 计算类型属性映射
     * @param resultMap      结果Map
     * @param timeGroups     时间分组列表
     * @return 是否需要重新计算
     */
    private boolean needRecalculateBaseIndicator(String sourceProperty,
                                                 Map<String, List<String>> calTypePropMap,
                                                 ConcurrentHashMap<String, ConcurrentHashMap<String, BigDecimal>> resultMap,
                                                 List<String[]> timeGroups) {
        try {
            // 1. 检查是否为基础指标
            List<String> baseProps = calTypePropMap.getOrDefault(ConstantBase.BASE_PROP, Collections.emptyList());
            if (!baseProps.contains(sourceProperty)) {
                return false;
            }

            // 2. 检查结果Map中该属性的数据完整性
            ConcurrentHashMap<String, BigDecimal> propertyValues = resultMap.get(sourceProperty);
            if (propertyValues == null) {
                // 属性数据不存在，需要计算
                return true;
            }

            // 3. 检查数据点数是否与时间分组数一致
            int expectedCount = timeGroups.size();
            int actualCount = propertyValues.size();

            if (actualCount != expectedCount) {
                log.debug("属性{}的数据点数({})与时间分组数({})不一致，需要重新计算",
                        sourceProperty, actualCount, expectedCount);
                return true;
            }

            return false;
        } catch (Exception e) {
            log.warn("检查属性{}是否需要重新计算时发生异常: {}", sourceProperty, e.getMessage());
            return false;
        }
    }


    public static List<String[]> generateTimeGroups(String startTimeStr, String endTimeStr, int interval, String unit) {
        LocalDateTime startTime = LocalDateTime.parse(startTimeStr, DATE_TIME_FORMATTER);
        LocalDateTime endTime = LocalDateTime.parse(endTimeStr, DATE_TIME_FORMATTER);
        List<String[]> timeGroups = new ArrayList<>();
        // 对齐开始时间到最近的时间间隔边界
        LocalDateTime alignedStartTime = alignToIntervalBoundary(startTime, interval, unit);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(Date.from(alignedStartTime.atZone(ZoneId.systemDefault()).toInstant()));

        LocalDateTime currentStartTime = alignedStartTime;
        while (currentStartTime.isBefore(endTime)) {
            LocalDateTime groupStartTime = currentStartTime;

            switch (unit) {
                case "m":
                    calendar.add(Calendar.MINUTE, interval);
                    break;
                case "h":
                    calendar.add(Calendar.HOUR, interval);
                    break;
                case "d":
                    calendar.add(Calendar.DAY_OF_MONTH, interval);
                    break;
                case "n":
                    calendar.add(Calendar.MONTH, interval);
                    break;
                case "y":
                    calendar.add(Calendar.YEAR, interval);
                    break;
            }

            LocalDateTime groupEndTime = calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            if (groupEndTime.isAfter(endTime)) {
                groupEndTime = endTime;
            }

            timeGroups.add(new String[]{groupStartTime.format(DATE_TIME_FORMATTER), groupEndTime.format(DATE_TIME_FORMATTER)});
            currentStartTime = groupEndTime;
        }

        // 循环结束后的检查
        LocalDateTime lastGroupStart = LocalDateTime.parse(timeGroups.get(timeGroups.size() - 1)[0], DATE_TIME_FORMATTER);
        calendar.setTime(Date.from(lastGroupStart.atZone(ZoneId.systemDefault()).toInstant()));
        switch (unit) {
            case "m":
                calendar.add(Calendar.MINUTE, interval);
                break;
            case "h":
                calendar.add(Calendar.HOUR, interval);
                break;
            case "d":
                calendar.add(Calendar.DAY_OF_MONTH, interval);
                break;
            case "n":
                calendar.add(Calendar.MONTH, interval);
                break;
            case "y":
                calendar.add(Calendar.YEAR, interval);
                break;
        }

        LocalDateTime nextGroupStart = calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();

        if (nextGroupStart.equals(endTime)) {
            // 如果最后一个分组的开始时间加上时间间隔等于整体的结束时间，则添加一个新的分组
            timeGroups.add(new String[]{endTime.format(DATE_TIME_FORMATTER), endTime.format(DATE_TIME_FORMATTER)});
        }

        return timeGroups;
    }

    private static LocalDateTime alignToIntervalBoundary(LocalDateTime startTime, int interval, String unit) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant()));

        switch (unit) {
            case "m":
                int minute = calendar.get(Calendar.MINUTE);
                int alignedMinute = (minute / interval) * interval;
                calendar.set(Calendar.MINUTE, alignedMinute);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                break;
            case "h":
                int hour = calendar.get(Calendar.HOUR_OF_DAY);
                int alignedHour = (hour / interval) * interval;
                calendar.set(Calendar.HOUR_OF_DAY, alignedHour);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                break;
            case "d":
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                break;
            case "n":
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                break;
            case "y":
                calendar.set(Calendar.MONTH, 0); // January
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                break;
        }

        return calendar.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }
}