package com.siact.energy.cal.tool.service.ruleDetail;

import cn.hutool.core.util.ObjectUtil;
import com.siact.energy.cal.common.pojo.vo.energycal.RuleDetailVo;
import com.siact.energy.cal.tool.service.ruleDetail.impl.RuleDetailServiceImpl;
import com.siact.energy.cal.tool.entity.ruleDetail.RuleDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class RuleChangeEventListener {

    @Autowired
    private RuleDetailServiceImpl ruleDetailService;

    @EventListener
    public void handleRuleChangeEvent(RuleDetailServiceImpl.RuleChangeEvent event) {
        try {
            RuleDetail rule = event.getRule();
            switch (event.getChangeType()) {
                case ADD:
                case MODIFY:
                case ENABLE:
                case DISABLE:
                    updateRuleCache(rule);
                    break;
                case DELETE:
                    deleteRuleCache(rule);
                    break;
            }
        } catch (Exception e) {
            log.error("处理规则变更事件失败: {}", e.getMessage(), e);
        }
    }

    private void updateRuleCache(RuleDetail rule) {
        try {
            // 获取规则详情
            RuleDetailVo ruleDetailVo = ruleDetailService.getRuleDetailByDevproperty(rule);
            if(ObjectUtil.isNotEmpty(ruleDetailVo)){
                ruleDetailService.getInsRuleDetail(ruleDetailVo);
            }

        } catch (Exception e) {
            log.error("更新规则缓存失败: {}", rule.getDevProperty(), e);
        }
    }

    private void removeRuleCache(RuleDetail rule) {
        try {
            // 构建要删除的规则列表
            RuleDetailVo ruleDetailVo = ruleDetailService.getRuleDetailByDevproperty(rule);
            if (ruleDetailVo != null) {
                List<RuleDetailVo> ruleDetails = new ArrayList<>();
                ruleDetails.add(ruleDetailVo);
                ruleDetailService.removeBatchFromRuleDetailInstance(ruleDetails);
            }
        } catch (Exception e) {
            log.error("删除规则缓存失败: {}", rule.getDevProperty(), e);
        }
    }

    private void deleteRuleCache(RuleDetail rule) {
        try {
            // 构建要删除的规则列表
            RuleDetailVo ruleDetailVo = ruleDetailService.getRuleDetailByDevproperty(rule);
            if (ruleDetailVo != null) {
                List<RuleDetailVo> ruleDetails = new ArrayList<>();
                ruleDetails.add(ruleDetailVo);
                ruleDetailService.deleteBatchFromRuleDetailInstance(ruleDetails);
            }
        } catch (Exception e) {
            log.error("删除规则缓存失败: {}", rule.getDevProperty(), e);
        }
    }
}