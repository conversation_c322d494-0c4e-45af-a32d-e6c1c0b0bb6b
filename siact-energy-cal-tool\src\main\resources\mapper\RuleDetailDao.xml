<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siact.energy.cal.tool.dao.ruleDetail.RuleDetailDao">

    <resultMap type="com.siact.energy.cal.tool.entity.ruleDetail.RuleDetail" id="RuleDetailMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="ruleName" column="rule_name" jdbcType="VARCHAR"/>
        <result property="ruleDes" column="rule_des" jdbcType="VARCHAR"/>
        <result property="ruleType" column="rule_type" jdbcType="INTEGER"/>
        <result property="calType" column="cal_type" jdbcType="INTEGER"/>
        <result property="devCode" column="dev_code" jdbcType="VARCHAR"/>
        <result property="devName" column="dev_name" jdbcType="VARCHAR"/>
        <result property="devProperty" column="dev_property" jdbcType="VARCHAR"/>
        <result property="propName" column="prop_name" jdbcType="VARCHAR"/>
        <result property="ruleFormula" column="rule_formula" jdbcType="VARCHAR" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result property="ruleFormulaShow" column="rule_formula_show" jdbcType="VARCHAR" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result property="projectId" column="project_id" jdbcType="INTEGER"/>
        <result property="ruleColId" column="rule_col_id" jdbcType="INTEGER"/>
        <result property="activeState" column="active_state" jdbcType="INTEGER"/>
        <result property="creator" column="creator" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updater" column="updater" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into rule_detail(rule_name, rule_des, rule_type, cal_type, dev_code, dev_name, dev_property, prop_name,
        rule_formula, rule_formula_show, project_id, rule_col_id, active_state, creator, create_time, updater,
        update_time, deleted)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.ruleName}, #{entity.ruleDes}, #{entity.ruleType}, #{entity.calType}, #{entity.devCode},
            #{entity.devName}, #{entity.devProperty}, #{entity.propName}, #{entity.ruleFormula},
            #{entity.ruleFormulaShow}, #{entity.projectId}, #{entity.ruleColId}, #{entity.activeState},
            #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleted})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 - 支持多数据库 -->
    <insert id="insertOrUpdateBatch">
        <choose>
            <!-- MySQL数据库 -->
            <when test="_databaseId == 'mysql'">
                insert into rule_detail(rule_name, rule_des, rule_type, cal_type, dev_code, dev_name, dev_property, prop_name,
                rule_formula, rule_formula_show, project_id, rule_col_id, active_state, creator, create_time, updater,
                update_time, deleted)
                values
                <foreach collection="entities" item="entity" separator=",">
                    (#{entity.ruleName}, #{entity.ruleDes}, #{entity.ruleType}, #{entity.calType}, #{entity.devCode},
                    #{entity.devName}, #{entity.devProperty}, #{entity.propName}, #{entity.ruleFormula},
                    #{entity.ruleFormulaShow}, #{entity.projectId}, #{entity.ruleColId}, #{entity.activeState},
                    #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleted})
                </foreach>
                on duplicate key update
                rule_name = values(rule_name), rule_des = values(rule_des), rule_type = values(rule_type), cal_type = values(cal_type),
                dev_code = values(dev_code), dev_name = values(dev_name), dev_property = values(dev_property), prop_name = values(prop_name),
                rule_formula = values(rule_formula), rule_formula_show = values(rule_formula_show), project_id = values(project_id),
                rule_col_id = values(rule_col_id), active_state = values(active_state), creator = values(creator), create_time = values(create_time),
                updater = values(updater), update_time = values(update_time), deleted = values(deleted)
            </when>
            <!-- PostgreSQL数据库（包括南大通用） -->
            <when test="_databaseId == 'postgresql'">
                insert into rule_detail(rule_name, rule_des, rule_type, cal_type, dev_code, dev_name, dev_property, prop_name,
                rule_formula, rule_formula_show, project_id, rule_col_id, active_state, creator, create_time, updater,
                update_time, deleted)
                values
                <foreach collection="entities" item="entity" separator=",">
                    (#{entity.ruleName}, #{entity.ruleDes}, #{entity.ruleType}, #{entity.calType}, #{entity.devCode},
                    #{entity.devName}, #{entity.devProperty}, #{entity.propName}, #{entity.ruleFormula},
                    #{entity.ruleFormulaShow}, #{entity.projectId}, #{entity.ruleColId}, #{entity.activeState},
                    #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleted})
                </foreach>
                on conflict (id) do update set
                rule_name = excluded.rule_name, rule_des = excluded.rule_des, rule_type = excluded.rule_type, cal_type = excluded.cal_type,
                dev_code = excluded.dev_code, dev_name = excluded.dev_name, dev_property = excluded.dev_property, prop_name = excluded.prop_name,
                rule_formula = excluded.rule_formula, rule_formula_show = excluded.rule_formula_show, project_id = excluded.project_id,
                rule_col_id = excluded.rule_col_id, active_state = excluded.active_state, creator = excluded.creator, create_time = excluded.create_time,
                updater = excluded.updater, update_time = excluded.update_time, deleted = excluded.deleted
            </when>
            <!-- 默认使用MySQL语法 -->
            <otherwise>
                insert into rule_detail(rule_name, rule_des, rule_type, cal_type, dev_code, dev_name, dev_property, prop_name,
                rule_formula, rule_formula_show, project_id, rule_col_id, active_state, creator, create_time, updater,
                update_time, deleted)
                values
                <foreach collection="entities" item="entity" separator=",">
                    (#{entity.ruleName}, #{entity.ruleDes}, #{entity.ruleType}, #{entity.calType}, #{entity.devCode},
                    #{entity.devName}, #{entity.devProperty}, #{entity.propName}, #{entity.ruleFormula},
                    #{entity.ruleFormulaShow}, #{entity.projectId}, #{entity.ruleColId}, #{entity.activeState},
                    #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime}, #{entity.deleted})
                </foreach>
                on duplicate key update
                rule_name = values(rule_name), rule_des = values(rule_des), rule_type = values(rule_type), cal_type = values(cal_type),
                dev_code = values(dev_code), dev_name = values(dev_name), dev_property = values(dev_property), prop_name = values(prop_name),
                rule_formula = values(rule_formula), rule_formula_show = values(rule_formula_show), project_id = values(project_id),
                rule_col_id = values(rule_col_id), active_state = values(active_state), creator = values(creator), create_time = values(create_time),
                updater = values(updater), update_time = values(update_time), deleted = values(deleted)
            </otherwise>
        </choose>
    </insert>
    <select id="listPage" resultType="com.siact.energy.cal.tool.entity.ruleDetail.RuleDetail">
        SELECT
            rd.*,
            rc.rule_col_name
        FROM
            rule_detail rd,
            rule_col rc
        WHERE
            rc.id = rd.rule_col_id
          AND rd.deleted = 0
        <if test="query.ruleName != null and query.ruleName != ''">
            AND rd.rule_name LIKE CONCAT('%', #{query.ruleName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="query.ruleType != null">
            AND rd.rule_type = #{query.ruleType,jdbcType=INTEGER}
        </if>
        <if test="query.calType != null">
            AND rd.cal_type = #{query.calType,jdbcType=INTEGER}
        </if>
        <if test="query.devName != null and query.devName != ''">
            AND rd.dev_name LIKE CONCAT('%', #{query.devName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="query.propName != null and query.propName != ''">
            AND rd.prop_name LIKE CONCAT('%', #{query.propName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="query.activeState != null">
            AND rd.active_state = #{query.activeState,jdbcType=INTEGER}
        </if>
        <if test="query.ruleColName != null and query.ruleColName != ''">
            AND rc.rule_col_name LIKE CONCAT('%', #{query.ruleColName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="query.projectId != null">
    <!-- AND (rd.rule_type = 1 OR (rd.rule_type = 0 AND rd.project_id = #{query.projectId,jdbcType=BIGINT}))-->
            AND rd.project_id = #{query.projectId,jdbcType=BIGINT}
 </if>
 ORDER BY rd.create_time DESC
</select>
<!-- 将rule_detail表和data_project表根据projectid join，获取项目编码 -->
    <select id="queryByCalType" parameterType="java.lang.Integer" resultType="com.siact.energy.cal.common.pojo.vo.energycal.RuleDetailVo">
        SELECT a.rule_type, a.cal_type, a.dev_code, a.dev_property, a.rule_formula,
               b.project_code,a.active_state,a.deleted FROM rule_detail a LEFT JOIN data_project b ON a.project_id = b.id
        WHERE a.cal_type = #{calType} AND a.active_state = 0 and a.deleted = 0 AND a.dev_property IS NOT NULL
    </select>

    <!-- 根据项目id和设备属性编码获取指标详情 -->
    <select id="getFormulaDetailByDevproperty" resultType="com.siact.energy.cal.common.pojo.vo.energycal.RuleDetailVo">
        SELECT a.id,a.rule_name,a.rule_type, a.cal_type, a.dev_code,a.dev_name, a.dev_property,a.prop_name, a.rule_formula,a.project_id,b.project_code,a.rule_col_id,a.active_state,a.deleted
            FROM rule_detail a LEFT JOIN data_project b ON a.project_id = b.id
        WHERE a.project_id = #{projectId} AND a.dev_property = #{devProperty} AND a.deleted = 0
    </select>


    <select id="getAllFormulaDetail" parameterType="java.lang.Integer" resultType="com.siact.energy.cal.common.pojo.vo.energycal.RuleDetailVo">
        SELECT a.id,a.rule_name,a.rule_des,a.rule_type, a.cal_type, a.dev_code,a.dev_name, a.dev_property,a.prop_name, a.rule_formula,a.rule_formula_show,a.project_id,
               b.project_code,a.rule_col_id,a.active_state,a.deleted FROM rule_detail a LEFT JOIN data_project b ON a.project_id = b.id
        WHERE a.dev_property IS NOT NULL
    </select>

    <select id="getRuleListByIds" resultType="com.siact.energy.cal.common.pojo.vo.energycal.RuleDetailVo">
        SELECT a.id,a.rule_type, a.cal_type, a.dev_code,a.dev_name, a.dev_property,a.prop_name, a.rule_formula,a.project_id,
               b.project_code,a.rule_col_id,a.active_state,a.deleted FROM rule_detail a LEFT JOIN data_project b ON a.project_id = b.id
        WHERE a.deleted = 0 AND a.dev_property IS NOT NULL AND a.id in (
            <foreach item="id" collection="list" separator=",">
                #{id}
            </foreach>
        )
    </select>

</mapper>

