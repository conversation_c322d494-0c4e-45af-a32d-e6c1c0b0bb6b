package com.siact.energy.cal.tool.service.flow.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siact.energy.cal.common.core.domain.R;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.flow.*;
import com.siact.energy.cal.common.pojo.enums.ComFlowStatusEnum;
import com.siact.energy.cal.common.pojo.vo.flow.*;
import com.siact.energy.cal.tool.common.flow.ELParser;
import com.siact.energy.cal.tool.common.flow.context.CalculateContext;
import com.siact.energy.cal.tool.common.flow.logicflow.ELNode;
import com.siact.energy.cal.tool.common.flow.logicflow.LfNode;
import com.siact.energy.cal.tool.common.flow.logicflow.LogicFlow;
import com.siact.energy.cal.tool.common.flow.logicflow.LogicFlowParser;
import com.siact.energy.cal.tool.convertor.flow.ComponentFlowConvertor;
import com.siact.energy.cal.tool.dao.flow.ComponentFlowMapper;
import com.siact.energy.cal.tool.dao.flow.SiComRelationMapper;
import com.siact.energy.cal.tool.dao.flow.SiLiteflowChainMapper;
import com.siact.energy.cal.tool.entity.flow.*;
import com.siact.energy.cal.tool.entity.flow.ComponentFlowEntity;
import com.siact.energy.cal.tool.entity.flow.FlowViewEntity;
import com.siact.energy.cal.tool.entity.flow.SiComRelationEntity;
import com.siact.energy.cal.tool.entity.flow.SiLiteflowChainEntity;
import com.siact.energy.cal.tool.service.feign.ProcessFeignClient;
import com.siact.energy.cal.tool.service.flow.IProcessService;
import com.siact.energy.cal.tool.service.flow.IRuleDetailFlowService;
import com.siact.energy.cal.tool.service.flow.ISiLiteflowChainService;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/**
 * @Author: 李飞
 * @CreateTime: 2024-05-30
 * @Description: 算子流服务类
 * @Version: 1.0
 */
@Service
@Slf4j
public class ProcessServiceImpl extends ServiceImpl<ComponentFlowMapper, ComponentFlowEntity> implements IProcessService {

    @Value("${spring.application.name}")
    String appName;
    @Resource
    SiComRelationMapper siComRelationMapper;

    @Resource
    ComponentFlowMapper componentFlowMapper;

    @Resource
    SiLiteflowChainMapper siLiteflowChainMapper;

    @Resource
    ISiLiteflowChainService siLiteflowChainService;

    @Resource
    ProcessFeignClient processFeignClient;

    @Override
    public int saveComponent(LogicFlow logicFlow) {
        ComponentFlowEntity componentFlowEntity = new ComponentFlowEntity();
        String sqlTemplate = null;
        try {
            ELParser elParser = new LogicFlowParser(logicFlow);
            ELNode elNode = elParser.extractElNode();
            sqlTemplate = elNode.generateEl();
        } catch (Exception e) {
            log.error("logicFlow转化el表达式失败,{}",e.getMessage(), e);
            throw new BizException(e.getMessage());
        }
        Integer flowId = Math.toIntExact(logicFlow.getFlowId());
        //查询组件是否已存在，存在则更新，不存在插入
        flowId = getComFlowId(logicFlow, componentFlowEntity, flowId);

        try {
            //保存组件表达式
            saveSiChainData(logicFlow, flowId, sqlTemplate);
        }catch (Exception e){
            log.error("刷新规则失败,{}",e.getMessage(), e);
            //回退数据
            throw new BizException(e.getMessage());
        }
        return flowId;
    }

    private void saveSiChainData(LogicFlow logicFlow, Integer flowId, String sqlTemplate) {
        LambdaQueryWrapper<SiLiteflowChainEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SiLiteflowChainEntity::getChainName, String.valueOf(flowId));
        wrapper.eq(SiLiteflowChainEntity::getEnable, true);
        SiLiteflowChainEntity chainEntity = siLiteflowChainMapper.selectOne(wrapper);
        if (chainEntity != null){
            chainEntity.setChainDesc(logicFlow.getFlowName());
            chainEntity.setElData(sqlTemplate);
            siLiteflowChainService.updateById(chainEntity);
        }else {
            SiLiteflowChainEntity siLiteflowChainEntity = new SiLiteflowChainEntity();
            siLiteflowChainEntity.setChainName(String.valueOf(flowId));
            siLiteflowChainEntity.setChainDesc(logicFlow.getFlowName());
            siLiteflowChainEntity.setElData(sqlTemplate);
            siLiteflowChainEntity.setApplicationName(appName);
            siLiteflowChainEntity.setEnable(true);
            siLiteflowChainService.save(siLiteflowChainEntity);
        }
    }

    /**
     * 校验组件流是否存在。后期增加人员校验
     * @param flowName 组件流名称
     */
    private void validatedFlowName(String flowName) {
        //校验组件流名称是否重复
        LambdaQueryWrapper<ComponentFlowEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ComponentFlowEntity::getFlowName, flowName);
        if (componentFlowMapper.selectOne(wrapper) != null){
            throw new BizException("组件流名称已存在,请重新命名!");
        }
    }

    /**
     * 插入组件流组表及关系表
     * @param logicFlow 组件流信息
     * @param componentFlowEntity 组件流对象
     * @param flowId 组件id
     * @return 组件id
     */
    private Integer getComFlowId(LogicFlow logicFlow, ComponentFlowEntity componentFlowEntity, Integer flowId) {
        ComponentFlowEntity componentFlowEntityById = componentFlowMapper.selectById(logicFlow.getFlowId());
        if (componentFlowEntityById == null){
            componentFlowEntity.setFlowName(logicFlow.getFlowName());
            componentFlowEntity.setContext(JSON.toJSONString(logicFlow,true));
            //目前无权限，创建人统计做为admin
            componentFlowEntity.setCreateName("admin");
            componentFlowMapper.insert(componentFlowEntity);
            flowId = componentFlowEntity.getId();
            //插入组件关系表数据-si_com_relation
            insertComRelation(logicFlow, flowId);
        }else {
            componentFlowEntityById.setFlowName(logicFlow.getFlowName());
            componentFlowEntityById.setContext(JSON.toJSONString(logicFlow,true));
            componentFlowMapper.updateById(componentFlowEntityById);
            //更新详组件细信息表-si_com_relation
            //先删除组件关系表数据再插入;
            siComRelationMapper.deleteByFlowId(flowId);
            insertComRelation(logicFlow, flowId);
        }
        return flowId;
    }

    private void updateComRelation(LogicFlow logicFlow, Integer flowId) {
        List<LfNode> nodes = logicFlow.getNodes();
        for (LfNode node : nodes) {
            LambdaQueryWrapper<SiComRelationEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SiComRelationEntity::getNodeId, node.getProperties().get("nodeId"));
            wrapper.eq(SiComRelationEntity::getFlowId, flowId);
            SiComRelationEntity siComRelationEntity = siComRelationMapper.selectOne(wrapper);
            JSONObject formConfig = JSONUtil.parseObj(node.getProperties().get("formConfig"));
            Object formItemList = formConfig.get("formItemList");
            if (siComRelationEntity != null){
                siComRelationEntity.setType(node.getType());
                siComRelationEntity.setText(formItemList!=null ? formItemList.toString() : null);
                getCalFormula(node, formItemList, siComRelationEntity);
                siComRelationMapper.updateById(siComRelationEntity);
            }else {
                SiComRelationEntity insertComRelationEntity = new SiComRelationEntity();
                insertComRelationEntity.setFlowId(flowId);
                insertComRelationEntity.setNodeId(node.getProperties().get("nodeId").toString());
                insertComRelationEntity.setText(formItemList!=null ? formItemList.toString() : null);
                insertComRelationEntity.setType(node.getType());
                getCalFormula(node, formItemList, insertComRelationEntity);
                siComRelationMapper.insert(insertComRelationEntity);
            }

        }
    }

    private static void getCalFormula(LfNode node, Object formItemList, SiComRelationEntity siComRelationEntity) {
        if (node.getType().contains("CommonNode") && formItemList !=null){
            List<CommonNodeDto> list = JSONUtil.toList(formItemList.toString(), CommonNodeDto.class);
            Map<String, Object> collect = list.stream().collect(Collectors.toMap(CommonNodeDto::getProp, CommonNodeDto::getValue));
            CalculationDto calculation = BeanUtil.toBean(collect, CalculationDto.class);
            calculation.setCalFormula(calculation.getCalFormula());
            siComRelationEntity.setFormula(calculation.getCalFormula().toString());
        }
    }

    private void insertComRelation(LogicFlow logicFlow, Integer flowId)  {
        List<LfNode> nodes = logicFlow.getNodes();
        for (LfNode node : nodes) {
            SiComRelationEntity siComRelationEntity = new SiComRelationEntity();
            if (node.getProperties().get("nodeId") != null){
                siComRelationEntity.setNodeId(node.getProperties().get("nodeId").toString());
                siComRelationEntity.setType(node.getType());
                JSONObject formConfigObject = JSONUtil.parseObj(node.getProperties().get("formConfig"));
                Object formItemList = formConfigObject.get("formItemList");
                siComRelationEntity.setText( formItemList!=null ? formItemList.toString() : null);
                node.getProperties().get("formConfig");
                siComRelationEntity.setFlowId(flowId);
                getCalFormula(node, formItemList, siComRelationEntity);
                siComRelationMapper.insert(siComRelationEntity);
            }
        }
    }

   /* @Override
    public FlowRunResultVO runFlow(String id, List<ParamDto> paramList) {
        if (StrUtil.isBlank(id)){
            throw new BizException("流程id不能为空！");
        }
        ComponentFlowEntity com = this.getById(id);
        if (com == null ){
            throw new BizException("组件流不存在!");
        }
        if (com.getStatus() == 1) {
            throw new BizException("组件流运行中，请稍后运行!");
        }
        ResultContext resultContext = new ResultContext();
        String taskId = UUIDUtils.uuidTaskName();
        resultContext.setTaskId(taskId);
        resultContext.setParamList(paramList);
        //查询该组件流配置的所有公式
        Set<String> comFlowFormulaSet = getComFlowFormula(id);

        List<RuleDetailEntity> list = ruleDetailFlowService.list();
        List<Long> collect = list.stream().map(RuleDetailEntity::getId).collect(toList());

        List<String> idList = new ArrayList<>();
        collect.forEach(item ->{
            idList.add(String.valueOf(item));
        });
        resultContext.setFormulaList(idList);
        FlowRunResultVO flowRunResultVO = new FlowRunResultVO();
        try {
            CompletableFuture<LiteflowResponse> completableFuture = CompletableFuture.supplyAsync(() -> flowExecutor.execute2Resp(id, "arg", resultContext));
            LiteflowResponse response = completableFuture.get();
            if (response.isSuccess()){
                this.updateComponentStatus(id, ComFlowStatusEnum.SUCCESS.getValue());
                log.info("组件流执行成功!");
                flowRunResultVO.setFlowId(id);
                flowRunResultVO.setFlowName("流程名称");
                Map<String, FlowRunResultDetailVO> nodeResult = new HashMap<>();
                LambdaQueryWrapper<FlowViewEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(FlowViewEntity::getFlowId, id);
                wrapper.eq(FlowViewEntity::getTaskId, taskId);
                List<FlowViewEntity> list1 = flowViewService.list(wrapper);
                for (FlowViewEntity flowViewEntity : list1) {
                    String tabId = flowViewEntity.getType() == 2 ? flowViewEntity.getTableName() : flowViewEntity.getViewName();
                    nodeResult.put(flowViewEntity.getComId(), getTabResult(tabId));
                }
                flowRunResultVO.setNodeResult(nodeResult);
            }else {
                log.info("组件流执行失败!");
                this.updateComponentStatus(id, ComFlowStatusEnum.FAIL.getValue());
                throw new BizException(response.getCause().getMessage());
            }
        }catch (Exception e){
            log.info("组件流执行失败!{}", e.getMessage(), e);
            this.updateComponentStatus(id, ComFlowStatusEnum.FAIL.getValue());
            throw new BizException(e.getMessage());

        }
        return flowRunResultVO;
    }*/
    @Override
    public FlowRunResultVO runFlow(String id, List<ParamDto> paramList) {
        R<FlowRunResultVO> flowRunResultVOR = processFeignClient.runFlow(id, paramList);
        return flowRunResultVOR.getData();
    }


    @Override
    public String getFlowContent(String flowId) {
        ComponentFlowEntity componentFlowEntity = componentFlowMapper.selectById(flowId);
        if (componentFlowEntity == null){
            return "查询的组件流不存在";
        }
        return componentFlowEntity.getContext();
    }

    @Override
    public PageBean<ComponentFlowVO> list(Page<ComponentFlowEntity> page, ComponentFlowDto componentFlowDto) {
        ComponentFlowConvertor instance = ComponentFlowConvertor.INSTANCE;
        LambdaQueryWrapper<ComponentFlowEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(!StrUtil.isBlank(componentFlowDto.getFlowName()),ComponentFlowEntity::getFlowName, componentFlowDto.getFlowName());
        wrapper.like(!StrUtil.isBlank(componentFlowDto.getCreateName()),ComponentFlowEntity::getCreateName, componentFlowDto.getCreateName());
        wrapper.gt(!StrUtil.isBlank(componentFlowDto.getCreateTimeStart()),ComponentFlowEntity::getCreateTime, componentFlowDto.getCreateTimeStart());
        wrapper.le(!StrUtil.isBlank(componentFlowDto.getUpdateTimeEnd()),ComponentFlowEntity::getCreateTime, componentFlowDto.getUpdateTimeEnd());
        wrapper.orderByDesc(ComponentFlowEntity::getCreateTime);
        Page<ComponentFlowEntity> componentFlowEntityPage = componentFlowMapper.selectPage(page, wrapper);
        List<ComponentFlowVO> componentFlowVOS = instance.entityToVoList(componentFlowEntityPage.getRecords());
        PageBean<ComponentFlowVO> pageBean = new PageBean<>();
        pageBean.setSize(componentFlowEntityPage.getSize());
        pageBean.setCurrent(page.getCurrent());
        pageBean.setTotal(componentFlowEntityPage.getTotal());
        pageBean.setRecords(componentFlowVOS);
        return pageBean;
    }

    @Override
    @Transactional
    public Boolean deleteFlow(List<Long> ids) {
        List<ComponentFlowEntity> componentFlowEntities = componentFlowMapper.selectBatchIds(ids);
        //删除组件表
        componentFlowMapper.deleteBatchIds(ids);
        componentFlowEntities.forEach(componentFlowEntity -> {
            //删除关系表
            siComRelationMapper.delete(new LambdaQueryWrapper<SiComRelationEntity>().eq(SiComRelationEntity::getFlowId, componentFlowEntity.getId()));
        });
        return true;
    }

    @Override
    public Integer copy(ComponentFlowDto componentFlowDto) {
        ComponentFlowEntity componentFlowEntity = componentFlowMapper.selectById(componentFlowDto.getId());
        if (componentFlowEntity == null){
            throw new BizException("源组件流不存在,复制失败");
        }
        validatedFlowName(componentFlowDto.getFlowName());
        componentFlowEntity.setFlowName(componentFlowDto.getFlowName());
        componentFlowEntity.setId(null);
        componentFlowEntity.setCreateTime(new Date());
        componentFlowEntity.setFlowDec(componentFlowDto.getFlowDec());
        componentFlowMapper.insert(componentFlowEntity);
        return componentFlowEntity.getId();
    }

    @Override
    public List<ComFlowNameVo> getComFlowList() {
        List<ComponentFlowEntity> list = this.list();
        List<ComFlowNameVo> comFlowNameVos = new ArrayList<>();
        for (ComponentFlowEntity componentFlowEntity : list) {
            ComFlowNameVo comFlowNameVo = new ComFlowNameVo();
            comFlowNameVo.setId(componentFlowEntity.getId());
            comFlowNameVo.setFlowName(componentFlowEntity.getFlowName());
            comFlowNameVos.add(comFlowNameVo);
        }
        return comFlowNameVos;
    }

    @Override
    public Boolean check(String flowName) {
        if (StrUtil.isBlank(flowName)){
            return false;
        }
        LambdaQueryWrapper<ComponentFlowEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ComponentFlowEntity::getFlowName, flowName.trim());
        return componentFlowMapper.selectOne(wrapper) != null;
    }

    @Override
    public void updateComponentStatus(String flowId, Integer status) {
        ComponentFlowEntity com = this.getById(flowId);
        if (com == null){
            return;
        }
        com.setStatus(status);
        this.updateById(com);
    }
}
