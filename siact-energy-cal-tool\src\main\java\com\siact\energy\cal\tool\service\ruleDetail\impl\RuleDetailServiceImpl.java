package com.siact.energy.cal.tool.service.ruleDetail.impl;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import com.googlecode.aviator.AviatorEvaluator;
import com.siact.api.common.api.vo.common.*;
import com.siact.api.feign.api.ins.InsService;
import com.siact.api.feign.api.model.ModelService;
import com.siact.code.util.InsCodeUtil;
import com.siact.common.core.enums.NodeTypeEnum;
import com.siact.common.core.enums.PropertyGroupEnum;
import com.siact.common.core.vo.PageVo;
import com.siact.energy.cal.common.core.exception.BizException;
import com.siact.energy.cal.common.datasource.common.PageBean;
import com.siact.energy.cal.common.pojo.dto.energycal.DevPropertyKey;
import com.siact.energy.cal.common.pojo.dto.energycal.DevPropertyValue;
import com.siact.energy.cal.common.pojo.dto.ruleDetail.*;
import com.siact.energy.cal.common.pojo.enums.*;
import com.siact.energy.cal.common.pojo.vo.common.DigitalTwinTreeVO;
import com.siact.energy.cal.common.pojo.vo.common.SelectOptionVO;
import com.siact.energy.cal.common.pojo.vo.dataProject.DataProjectVO;
import com.siact.energy.cal.common.pojo.vo.energycal.RuleDetailVo;
import com.siact.energy.cal.common.pojo.vo.ruleCol.AttrObject;
import com.siact.energy.cal.common.pojo.vo.ruleDetail.FormulaTreeVO;
import com.siact.energy.cal.common.pojo.vo.ruleDetail.RuleDetailVO;
import com.siact.energy.cal.common.pojo.vo.ruleDetail.RuleFormulaDetail;
import com.siact.energy.cal.common.util.utils.*;
import com.siact.energy.cal.tool.common.config.IOThreadPoolConfig;
import com.siact.energy.cal.tool.convertor.common.DigitalTwinConvertor;
import com.siact.energy.cal.tool.convertor.ruleDetail.RuleDetailConvertor;
import com.siact.energy.cal.tool.dao.ruleDetail.RuleDetailDao;
import com.siact.energy.cal.tool.entity.ruleDetail.FormulaDetail;
import com.siact.energy.cal.tool.entity.ruleDetail.RuleDetail;
import com.siact.energy.cal.tool.entity.ruleDetail.RuleDetailInstance;
import com.siact.energy.cal.tool.service.dataProject.DataProjectService;
import com.siact.energy.cal.tool.service.dataProject.impl.DataProjectServiceImpl;
import com.siact.energy.cal.tool.service.ruleCol.RuleColService;
import com.siact.energy.cal.tool.service.ruleDetail.RuleDetailInstanceService;
import com.siact.energy.cal.tool.service.ruleDetail.RuleDetailService;
import com.siact.energy.cal.tool.service.BaseService;
import com.siact.ins.server.common.vo.common.InsTreeVo;
import com.siact.model.vo.common.ModelTreeVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import java.io.IOException;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 指标详情表(RuleDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-21 10:05:48
 */
@Slf4j
@Service("ruleDetailService")
public class RuleDetailServiceImpl extends ServiceImpl<RuleDetailDao, RuleDetail> implements RuleDetailService {

    @Autowired
    private RuleDetailDao ruleDetailDao;

    @Autowired
    private ModelService modelService;

    @Autowired
    private InsService insService;

    @Autowired
    private DataProjectService dataProjectService;

    @Autowired
    private DataProjectServiceImpl dataProjectServiceImpl;

    @Autowired
    private RuleColService ruleColService;

    @Autowired
    @Qualifier(IOThreadPoolConfig.IO_THREAD_POOL_NAME)
    private Executor executor;

    @Autowired
    private RuleDetailInstanceService ruleDetailInstanceService;

    @Autowired
    private BaseService baseService;

    @Value("${digitalTwin.api.url}")
    private String dataTwinsUrl;
    private final String prefix = "@[";
    private final String suffix = "]";


    // 在类的顶部，其他内部类定义的位置添加
    @Data
    @AllArgsConstructor
    private static class ModelInstanceMappingResult {
        private Map<String, Map<String, String>> modelInsMap;
        private Map<String, String> devPropertyNameMap;
    }
    /**
     * 模型树缓存名称
     */
    private static final String MODEL_TREE_CACHE_NAMES = "modelTree";

    /**
     * 实例树缓存名称
     */
    private static final String INS_TREE_CACHE_NAMES = "insTree";

    /**
     * 属性组后缀
     */
    public static final String PROP_GROUP_SUFFIX = "Group";

    /**
     * 空字符串
     */
    public static final String EMPTY_STR = "";

    /**
     * 中杠
     */
    public static final String CENTER_BAR = "-";

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    /**
     * 规则变更事件
     */
    @Getter
    public static class RuleChangeEvent extends ApplicationEvent {
        private final RuleDetail rule;
        private final RuleDetail oldRule;
        private final ChangeType changeType;

        public RuleChangeEvent(Object source, RuleDetail rule, RuleDetail oldRule, ChangeType changeType) {
            super(source);
            this.rule = rule;
            this.oldRule = oldRule;
            this.changeType = changeType;
        }

        public enum ChangeType {
            ADD,        // 新增
            DELETE,     // 删除
            MODIFY,     // 修改
            ENABLE,     // 启用
            DISABLE     // 禁用
        }
    }

    /**
     * 发布规则变更事件
     */
    private void publishRuleChangeEvent(RuleDetail rule, RuleDetail oldRule, RuleChangeEvent.ChangeType changeType) {
        try {
            eventPublisher.publishEvent(new RuleChangeEvent(this, rule, oldRule, changeType));
        } catch (Exception e) {
            log.error("发布规则变更事件失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public PageBean<RuleDetailVO> listPage(PageBean<RuleDetailVO> page, RuleDetailQueryDTO ruleDetailQueryDTO) {
        // 如果projectId为空，返回空的指标详情列表
        if (ruleDetailQueryDTO.getProjectId() == null) {
            PageBean<RuleDetailVO> emptyPage = new PageBean<>();
            emptyPage.setRecords(new ArrayList<>());
            emptyPage.setTotal(0);
            emptyPage.setCurrent(page.getCurrent());
            emptyPage.setSize(page.getSize());
            return emptyPage;
        }

        // FIXME 高卫东 自定义的查询方法，不能将字符串转为数组问题修复
        PageBean<RuleDetailVO> pageBean = RuleDetailConvertor.INSTANCE.entityPage2VoPageBean(ruleDetailDao.listPage(page, ruleDetailQueryDTO));
        pageBean.getRecords().forEach(r -> {
            RuleDetailVO ruleDetailVO = getVoById(r.getId());
            r.setRuleFormula(ruleDetailVO.getRuleFormula());
            r.setRuleFormulaShow(ruleDetailVO.getRuleFormulaShow());
        });
        return pageBean;
    }

    @Override
    public RuleDetailVO getVoById(Serializable id) {
        RuleDetail ruleDetail = getById(id);
        return RuleDetailConvertor.INSTANCE.entity2Vo(ruleDetail);
    }

    @Override
    public List<RuleDetailVO> getDatacodeByRuleColId(Long ruleColId) {
        LambdaQueryWrapper<RuleDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RuleDetail::getRuleColId, ruleColId);
        return RuleDetailConvertor.INSTANCE.entities2Vos(list(queryWrapper));
    }

    @Override
    @Transactional
    public Boolean save(RuleDetailInsertDTO ruleDetailInsertDTO) {
        RuleDetail ruleDetail = RuleDetailConvertor.INSTANCE.insertDTO2Entity(ruleDetailInsertDTO);
        ruleDetail.setActiveState(ActiveStateEnum.ENABLE.getValue());
        ruleDetail.setDeleted(DeletedEnum.NORMAL.getValue());

        // 设置用户信息
        Long currentUserId = baseService.getLoginUserId();
        ruleDetail.setCreator(currentUserId);
        ruleDetail.setUpdater(currentUserId);

        boolean saved = save(ruleDetail);
        if (saved) {
            // 发布新增事件
            publishRuleChangeEvent(ruleDetail, null, RuleChangeEvent.ChangeType.ADD);

            // 更新指标集的attr_list列
//            Long ruleColId = ruleDetailInsertDTO.getRuleColId();
//            List<AttrObject> targetAttrList = getTargetAttrList(ruleDetailInsertDTO);
//            ruleColService.updateAttrList(ruleColId, targetAttrList);
        }
        return saved;
    }

    @Override
    @Transactional
    public Boolean updateVoById(RuleDetailUpdateDTO ruleDetailUpdateDTO) {
        RuleDetail oldRule = getById(ruleDetailUpdateDTO.getId());
        RuleDetail newRule = RuleDetailConvertor.INSTANCE.updateDTO2Entity(ruleDetailUpdateDTO);

        // 设置更新者信息
        Long currentUserId = baseService.getLoginUserId();
        newRule.setUpdater(currentUserId);

        boolean updated = updateById(newRule);
        if (updated) {
            // 发布修改事件
            publishRuleChangeEvent(newRule, oldRule, RuleChangeEvent.ChangeType.MODIFY);
        }
        return updated;
    }

    @Override
    public void verifyInsertParam(RuleDetailInsertDTO dto) {
        if (isExistByDevProperty(dto.getDevProperty(), dto.getProjectId(), dto.getRuleColId(), null)) {
            throw new BizException("当前输出指标已经存在指标表达式");
        }
        //判断是否存在循环依赖,即A=A+B 公式是不允许的
        if (iscircularDependency(dto)) {
            throw new BizException("当前表达式存在循环依赖,请修改输出指标");
        }
        ;
    }

    private Boolean iscircularDependency(RuleDetailInsertDTO dto) {
        String devProperty = dto.getDevProperty();
        List<String> ruleFormula = dto.getRuleFormula();
        return ruleFormula.contains(prefix + devProperty + suffix);
    }

    /**
     * 是否存在属性编码的表达式
     *
     * @param devProperty 属性编码
     * @param id          数据id
     * @return 是否存在
     */
    private boolean isExistByDevProperty(String devProperty, Long projectId, Long ruleColId, Long id) {
        LambdaQueryWrapper<RuleDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(RuleDetail::getDevProperty, devProperty)
                .eq(RuleDetail::getProjectId, projectId)
                .eq(RuleDetail::getRuleColId, ruleColId);

        List<RuleDetail> list = list(queryWrapper);
        boolean exist = !CollectionUtils.isEmpty(list) && !Objects.equals(id, list.get(0).getId());
        return exist;
    }

    /**
     * 根据RuleDetail获取规则详情
     */
    public RuleDetailVo getRuleDetailByDevproperty(RuleDetail rule) {
        if (rule == null) {
            return null;
        }
        return ruleDetailDao.getFormulaDetailByDevproperty(rule.getProjectId(), rule.getDevProperty());
    }

    /**
     * 根据RuleDetailInsertDTO获取规则详情
     */
    public RuleDetailVo getRuleDetailByDevproperty(RuleDetailInsertDTO ruleDetailInsertDTO) {
        if (ruleDetailInsertDTO == null) {
            return null;
        }
        return ruleDetailDao.getFormulaDetailByDevproperty(
                ruleDetailInsertDTO.getProjectId(),
                ruleDetailInsertDTO.getDevProperty()
        );
    }

    @Override
    public void verifyUpdateParam(RuleDetailUpdateDTO dto) {
        if (isExistByDevProperty(dto.getDevProperty(), dto.getProjectId(), dto.getRuleColId(), dto.getId())) {
            throw new BizException("当前输出指标已经存在指标表达式");
        }
    }

    @Override
    @Cacheable(cacheNames = MODEL_TREE_CACHE_NAMES, key = "#dataCode")
    public List<DigitalTwinTreeVO> modelTree(String dataCode) {

        List<DigitalTwinTreeVO> digitalTwinTreeVOList;
        String modelCode ="PGY02_S0000_ST00000_U00000_EQ000000000_MP0000";
        //获取项目实例对应的模型数字化编码
        if(!ConstantBase.ENERGY_DW.equals(dataCode)){
            modelCode = InsCodeUtil.getModelDataCodeByInsDataCode(dataCode);
        }

        // 获取数字孪生模型树
        R<List<ModelTreeVo>> listR = modelService.list(modelCode, true, true, false, false, true, true);
        if (Objects.equals(R.OK().getCode(), listR.getCode())) {

            List<ModelTreeVo> modelTreeVoList = listR.getData();
            // 转换为目标对象
            DigitalTwinConvertor convertor = DigitalTwinConvertor.INSTANCE;
            digitalTwinTreeVOList = convertor.modelTreeVos2DigitalTwinTreeVOs(modelTreeVoList);
            // 递归将编码分组
            Table<String, String, DigitalTwinTreeVO> table = HashBasedTable.create();
            buildDataCodeNodeTypeObjTable(digitalTwinTreeVOList, table);

            // 查询并拼装动态、静态属性
            // 按照节点类型分组
            Map<String, Map<String, DigitalTwinTreeVO>> nodeTypeVoMap = table.columnMap();
            // 分类遍历
            // TODO 高卫东 并发调用
            nodeTypeVoMap.forEach((k, v) -> {
                // 分批调用
                List<String> dataCodeList = Lists.newArrayList(v.keySet());
                Lists.partition(dataCodeList, 200).forEach(l -> {

                    ModelQueryPropertyDTO modelQueryPropertyDTO = new ModelQueryPropertyDTO();
                    modelQueryPropertyDTO.setDataCodes(l);
                    modelQueryPropertyDTO.setPageNumber(1);
                    modelQueryPropertyDTO.setPageSize(Integer.MAX_VALUE);

                    // 动态属性
                    R<PageVo<ModelPropertyVO>> dynamicPageVoR = modelService.pageDynamicProp(modelQueryPropertyDTO);
                    PropertyGroupEnum dynamicProp = PropertyGroupEnum.Dynamic;
                    if (Objects.equals(R.OK().getCode(), dynamicPageVoR.getCode())) {
                        fillModelPropData(convertor, table, dynamicPageVoR, dynamicProp);
                    } else {
                        log.error("获取动态属性失败：{}", dynamicPageVoR);
                        throw new BizException("获取动态属性失败");
                    }

                    // 静态属性
                    R<PageVo<ModelPropertyVO>> staticPageVoR = modelService.pageStaticProp(modelQueryPropertyDTO);
                    PropertyGroupEnum staticProp = PropertyGroupEnum.Static;
                    if (Objects.equals(R.OK().getCode(), staticPageVoR.getCode())) {
                        fillModelPropData(convertor, table, staticPageVoR, staticProp);
                    } else {
                        log.error("获取静态属性失败：{}", staticPageVoR);
                        throw new BizException("获取静态属性失败");
                    }

                });
            });

        } else {
            log.error("获取模型树失败：{}", listR);
            throw new BizException("获取模型树失败");
        }

        return digitalTwinTreeVOList;
    }

    /**
     * 填充属性数据
     *
     * @param convertor         转换器
     * @param table             节点数据
     * @param pageVoR           数字孪生分页数据
     * @param propertyGroupEnum 属性组
     */
    private static void fillModelPropData(DigitalTwinConvertor convertor, Table<String, String, DigitalTwinTreeVO> table, R<PageVo<ModelPropertyVO>> pageVoR, PropertyGroupEnum propertyGroupEnum) {

        // 获取到分页数据
        List<ModelPropertyVO> modelPropertyVOList = pageVoR.getData().getRecords();
        // 按照节点编码分组
        Map<String, List<ModelPropertyVO>> dataCodePropVOMap = modelPropertyVOList.stream().collect(Collectors.groupingBy(ModelPropertyVO::getModelDataCode));
        // 遍历
        dataCodePropVOMap.forEach((k, v) -> {
            // 转换为简化对象
            List<DigitalTwinTreeVO> propVOList = convertor.propVOs2DigitalTwinTreeVOs(v, propertyGroupEnum.getTag());
            // 构建属性根简化对象
            DigitalTwinTreeVO propVO = DigitalTwinTreeVO.builder()
                    .name(propertyGroupEnum.getDesc())
                    .nodeType(joinPropGroupField(propertyGroupEnum.getTag(), EMPTY_STR))
                    .dataCode(joinPropGroupField(k, CENTER_BAR)).children(propVOList).build();
            table.row(k).values().forEach(d -> {
                // 如果当前节点不存在存在子节点
                if (CollectionUtils.isEmpty(d.getChildren())) {
                    d.setChildren(Lists.newArrayList(propVO));
                }
                // 如果当前节点存在子节点
                else {
                    d.getChildren().add(0, propVO);
                }
            });
        });
    }

    /**
     * 拼接属性组信息
     *
     * @param str   待拼接字符串
     * @param split 分割符
     * @return 拼接结果
     */
    public static String joinPropGroupField(String str, String split) {
        return String.join(EMPTY_STR, str, split, PROP_GROUP_SUFFIX);
    }

    /**
     * 递归组装数字化编码、类型、对象集合
     *
     * @param digitalTwinTreeVOList 原始数据
     * @param table                 结果收集集合
     */
    private void buildDataCodeNodeTypeObjTable(List<DigitalTwinTreeVO> digitalTwinTreeVOList,
                                               Table<String, String, DigitalTwinTreeVO> table) {
        if (!CollectionUtils.isEmpty(digitalTwinTreeVOList)) {
            digitalTwinTreeVOList.forEach(d -> {
                table.put(d.getDataCode(), d.getNodeType(), d);
                buildDataCodeNodeTypeObjTable(d.getChildren(), table);
            });
        }
    }

    @Override
    @Cacheable(cacheNames = INS_TREE_CACHE_NAMES, key = "#dataCode")
    public List<DigitalTwinTreeVO> insTree(String dataCode) {
        List<DigitalTwinTreeVO> digitalTwinTreeVOList;
        StopWatch stopWatch = new StopWatch();

        // 特殊处理 ENERGY_DATAWAREHOUSE
        if ("ENERGY_DATA_WAREHOUSE".equals(dataCode)) {
            stopWatch.start("查询能源数据仓所有项目实例树");
            try {
                // 1. 获取所有项目
                List<DataProjectVO> projectByDigitalTwin = dataProjectServiceImpl.getProjectByDigitalTwin();
                if (ObjectUtil.isEmpty(projectByDigitalTwin)) {
                    log.error("获取项目列表失败：{}", projectByDigitalTwin);
                    throw new BizException("获取项目列表失败");
                }

                List<InsTreeVo> allInsTreeVos = new ArrayList<>();
                List<CompletableFuture<Void>> futures = new ArrayList<>();

                // 2. 并发获取每个项目的实例树
                for (DataProjectVO project : projectByDigitalTwin) {
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            R<List<InsTreeVo>> insListR = insService.list(
                                    project.getProjectCode(), true, true, false, false, true, true);
                            if (Objects.equals(R.OK().getCode(), insListR.getCode())) {
                                synchronized (allInsTreeVos) {
                                    allInsTreeVos.addAll(insListR.getData());
                                }
                            } else {
                                log.error("获取项目{}的实例树失败：{}", project.getProjectCode(), insListR);
                            }
                        } catch (Exception e) {
                            log.error("获取项目{}的实例树异常：", project.getProjectCode(), e);
                        }
                    }, executor);  // 使用线程池执行异步任务

                    futures.add(future);
                }

                // 等待所有异步任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

                // 转换为目标对象
                DigitalTwinConvertor convertor = DigitalTwinConvertor.INSTANCE;
                digitalTwinTreeVOList = convertor.insTreeVos2DigitalTwinTreeVOs(allInsTreeVos);

            } catch (Exception e) {
                log.error("获取能源数据仓所有实例树失败：", e);
                throw new BizException("获取能源数据仓所有实例树失败");
            }
            stopWatch.stop();
        } else {
            // 原有逻辑
            stopWatch.start("查询数字孪生实例树");
            R<List<InsTreeVo>> listR = insService.list(dataCode, true, true, false, false, true, true);
            stopWatch.stop();
            if (Objects.equals(R.OK().getCode(), listR.getCode())) {
                List<InsTreeVo> insTreeVoList = listR.getData();
                DigitalTwinConvertor convertor = DigitalTwinConvertor.INSTANCE;
                digitalTwinTreeVOList = convertor.insTreeVos2DigitalTwinTreeVOs(insTreeVoList);
            } else {
                log.error("获取实例树失败：{}", listR);
                throw new BizException("获取实例树失败");
            }
        }

        log.info(stopWatch.prettyPrint());
        log.info("总用时：" + stopWatch.getTotalTimeMillis() + "ms");

        return digitalTwinTreeVOList;
    }


    @Override
    public List<DigitalTwinTreeVO> getProp(String dataCode, String nodeType) {

        List<DigitalTwinTreeVO> digitalTwinTreeVOListTree;
        // 获取数字孪生模型树
        // 转换为目标对象
        DigitalTwinConvertor convertor = DigitalTwinConvertor.INSTANCE;
        R<List<InsTreeVo>> listR = insService.list(dataCode, true, true, false, false, true, true);
        if (Objects.equals(R.OK().getCode(), listR.getCode())) {
            List<InsTreeVo> insTreeVoList = listR.getData();
            digitalTwinTreeVOListTree = convertor.insTreeVos2DigitalTwinTreeVOs(insTreeVoList);
        } else {
            log.error("获取实例树失败：{}", listR);
            throw new BizException("获取实例树失败");
        }
        StopWatch stopWatch = new StopWatch();

        stopWatch.start("本地组装数据");
        List<DigitalTwinTreeVO> digitalTwinTreeVOList = new ArrayList<>();
        DigitalTwinTreeVO digitalTwinTreeVO = new DigitalTwinTreeVO();
        digitalTwinTreeVO.setDataCode(dataCode);
        digitalTwinTreeVO.setNodeType(nodeType);
        digitalTwinTreeVOList.add(digitalTwinTreeVO);
        // 转换为目标对象
//        DigitalTwinConvertor convertor = DigitalTwinConvertor.INSTANCE;
        // 递归将编码分组
        Table<String, String, DigitalTwinTreeVO> table = HashBasedTable.create();
        buildDataCodeNodeTypeObjTable(digitalTwinTreeVOList, table);
        stopWatch.stop();

        // 查询并拼装动态、静态属性
        // 分批调用并发调用
        List<CompletableFuture<Void>> futureList = Lists.newArrayList();
        Map<String, Map<String, DigitalTwinTreeVO>> nodeTypeMap = table.columnMap();
        // 项目、系统、站点、单元一次性查询
        stopWatch.start("批量查询静态、动态属性");

        // 项目
        List<String> proDataCodes = Lists.newArrayList();
        if (nodeTypeMap.containsKey(NodeTypeEnum.Project.getTag())) {
            proDataCodes.addAll(nodeTypeMap.get(NodeTypeEnum.Project.getTag()).keySet());
            CompletableFuture<Void> proFuture = CompletableFuture.runAsync(() -> {
                splitQueryAndFillInsPropData(convertor, table, proDataCodes, "项目属性查询");
            }, executor);
            futureList.add(proFuture);
        }

        // 系统
        List<String> sysDataCodes = Lists.newArrayList();
        if (nodeTypeMap.containsKey(NodeTypeEnum.System.getTag())) {
            sysDataCodes.addAll(nodeTypeMap.get(NodeTypeEnum.System.getTag()).keySet());
            CompletableFuture<Void> sysFuture = CompletableFuture.runAsync(() -> {
                splitQueryAndFillInsPropData(convertor, table, sysDataCodes, "系统属性查询");
            }, executor);
            futureList.add(sysFuture);
        }

        // 站点
        List<String> stDataCodes = Lists.newArrayList();
        if (nodeTypeMap.containsKey(NodeTypeEnum.Station.getTag())) {
            stDataCodes.addAll(nodeTypeMap.get(NodeTypeEnum.Station.getTag()).keySet());
            CompletableFuture<Void> stFuture = CompletableFuture.runAsync(() -> {
                splitQueryAndFillInsPropData(convertor, table, stDataCodes, "站点属性查询");
            }, executor);
            futureList.add(stFuture);
        }

        // 单元
        List<String> uniDataCodes = Lists.newArrayList();
        if (nodeTypeMap.containsKey(NodeTypeEnum.Unit.getTag())) {
            uniDataCodes.addAll(nodeTypeMap.get(NodeTypeEnum.Unit.getTag()).keySet());
            CompletableFuture<Void> uniFuture = CompletableFuture.runAsync(() -> {
                splitQueryAndFillInsPropData(convertor, table, uniDataCodes, "单元属性查询");
            }, executor);
            futureList.add(uniFuture);
        }

        // 设备分批查询
        List<String> eqDataCodes = Lists.newArrayList();
        if (nodeTypeMap.containsKey(NodeTypeEnum.Equipment.getTag())) {
            eqDataCodes.addAll(nodeTypeMap.get(NodeTypeEnum.Equipment.getTag()).keySet());
            Lists.partition(eqDataCodes, 100).forEach(dataCodes -> {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    splitQueryAndFillInsPropData(convertor, table, dataCodes, "设备属性查询");
                }, executor);
                futureList.add(future);
            });
        }

        // 表计分批查询
        List<String> meterDataCodes = Lists.newArrayList();
        if (nodeTypeMap.containsKey(NodeTypeEnum.Meter.getTag())) {
            meterDataCodes.addAll(nodeTypeMap.get(NodeTypeEnum.Meter.getTag()).keySet());
            Lists.partition(meterDataCodes, 100).forEach(dataCodes -> {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    splitQueryAndFillInsPropData(convertor, table, dataCodes, "表计属性查询");
                }, executor);
                futureList.add(future);
            });
        }

        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[]{})).join();
        List<DigitalTwinTreeVO> digitalTwinTreeVOS = mergeLists(digitalTwinTreeVOList, digitalTwinTreeVOListTree);
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        log.info("总用时：" + stopWatch.getTotalTimeMillis() + "ms");
        return digitalTwinTreeVOS;
    }


    @Override
    public PageBean<RuleDetailViewDTO> viewFormula(Long id, PageBean<RuleDetailViewDTO> page) {
        // 获取规则详情
        RuleDetail ruleDetail = getById(id);
        if (ruleDetail == null) {
            throw new BizException("公式不存在");
        }

        // 判断是否为模型公式
        if (ruleDetail.getRuleType() == RuleTypeEnum.MODEL.getValue()) {
            //获取模型对应的实例属性
            DataProjectVO projectVO = dataProjectService.getVoById(ruleDetail.getProjectId());
            if (ObjectUtil.isNotEmpty(projectVO)) {
                // 模型公式，需要获取所有实例公式并分页
                List<RuleDetailViewDTO> instanceFormulas = getInstanceCodesByModelCode(
                        projectVO.getProjectCode(),
                        ruleDetail.getDevProperty()
                );

                // 设置通用属性
                instanceFormulas.forEach(ruleDetailViewDTO -> {
                    ruleDetailViewDTO.setRuleType(ruleDetail.getRuleType());
                    ruleDetailViewDTO.setCalType(ruleDetail.getCalType());
                    ruleDetailViewDTO.setRuleColId(String.valueOf(ruleDetail.getRuleColId()));
                    ruleDetailViewDTO.setProjectId(ruleDetail.getProjectId());
                });

                // 安全的分页处理
                int totalSize = instanceFormulas.size();
                int pageSize = (int) page.getSize();
                int currentPage = (int) page.getCurrent();

                // 计算实际的起始和结束索引
                int start = (currentPage - 1) * pageSize;
                // 确保start不会超出列表范围
                start = Math.min(start, totalSize);

                // 计算本页实际的结束索引
                int end = Math.min(start + pageSize, totalSize);

                // 获取当前页的数据
                List<RuleDetailViewDTO> pageData = start < end ?
                        instanceFormulas.subList(start, end) :
                        new ArrayList<>();

                // 设置分页信息
                page.setRecords(pageData);
                page.setTotal(totalSize);

                return page;
            } else {
                // 如果项目为空，返回空分页对象
                page.setRecords(new ArrayList<>());
                page.setTotal(0);
                return page;
            }
        } else {
            // 实例公式，直接返回单个结果
            String formula = getFormulaFromInstance(ruleDetail.getDevProperty());
            List<RuleDetailViewDTO> instanceFormulas = new ArrayList<>();
            instanceFormulas.add(buildRuleDetailViewDTO(ruleDetail, formula));
            page.setRecords(instanceFormulas);
            page.setTotal(1);
            return page;
        }
    }

    @Override
    public FormulaTreeDTO viewFormulaTree(FormulaTreeVO formulaTreeVO) {
        String devProperty = formulaTreeVO.getDevProperty();
        if (StrUtil.isBlank(devProperty)) {
            throw new BizException("设备属性不能为空");
        }

        Long ruleColID = ObjectUtil.isEmpty(formulaTreeVO.getRuleColId()) ?
                ConstantBase.RULECOLID_COMMON :
                formulaTreeVO.getRuleColId();

        RuleFormulaDetail rootFormula = getFormula(devProperty, ruleColID);
        if (rootFormula == null) {
            throw new BizException("未找到公式");
        }
        // 构建根节点
        FormulaTreeDTO root = buildFormulaNode(rootFormula, null);

        // 递归构建子节点
        buildChildrenNodes(root, ruleColID);

        return root;
    }

    /**
     * 构建公式节点
     *
     * @param formula  公式详情
     * @param parentId 父节点ID
     */
    private FormulaTreeDTO buildFormulaNode(RuleFormulaDetail formula, String parentId) {
        FormulaTreeDTO node = new FormulaTreeDTO();
        // 生成唯一ID
        node.setId(UUID.randomUUID().toString());
        node.setPid(parentId);
        node.setDevCode(formula.getDevCode());
        node.setDevName(formula.getDevName());
        node.setDevProperty(formula.getDevProperty());
        node.setPropName(formula.getPropName());
        node.setFormula(formula.getRuleFormula());
        node.setCalType(CalTypeEnum.getDescriptionByValue(formula.getCalType()));
        node.setChildren(new ArrayList<>());
        return node;
    }

    /**
     * 递归构建子节点
     */
    private void buildChildrenNodes(FormulaTreeDTO parent, Long ruleColId) {
        // 解析公式中的变量
        List<String> variables = FormulaUtils.getVarList(parent.getFormula());

        // 对每个变量递归构建子节点
        for (String variable : variables) {
            RuleFormulaDetail childFormula = getFormula(variable, ruleColId);
            if (childFormula != null) {
                // 传入父节点ID
                FormulaTreeDTO childNode = buildFormulaNode(childFormula, parent.getId());
                parent.getChildren().add(childNode);
                // 递归处理子节点
                buildChildrenNodes(childNode, ruleColId);
            } else {
                // 如果找不到公式,则将变量作为节点
                String devCode = InsCodeUtil.getParentDataCode(variable);
                String insName = getInsNameByDataCode(devCode);
                String propName = getPropNameByDataCode(variable);
                childFormula = new RuleFormulaDetail();
                childFormula.setDevCode(devCode);
                childFormula.setDevName(insName);
                childFormula.setDevProperty(variable);
                childFormula.setPropName(propName);
                childFormula.setRuleFormula("");
                FormulaTreeDTO childNode = buildFormulaNode(childFormula, parent.getId());
                parent.getChildren().add(childNode);
                // 递归处理子节点
//                buildChildrenNodes(childNode, ruleColId);
            }
        }
    }

    private List<RuleDetailViewDTO> getInstanceCodesByModelCode(String projectCode, String devProperty) {

        ArrayList<RuleDetailViewDTO> list = new ArrayList<>();

        // 构建请求URL
        String url = dataTwinsUrl + "/common/prop/ins";
        List<String> modelCodes = Collections.singletonList(devProperty);

        // 调用数字孪生接口获取实例信息
        String result = getInsByModelCode(url, modelCodes, projectCode);
        JSONArray array = CommonUtils.returnResultHandler(result);

        if (ObjectUtil.isNotEmpty(array)) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject jsObject = array.getJSONObject(i);
                String devCode = jsObject.getString("insDataCode");
                String propCode = jsObject.getString("propInsDataCode");
                String devName = jsObject.getString("insName");
                String propInsName = jsObject.getString("propInsName");
                //获取计算公式
                String formula = getFormulaFromInstance(propCode);
                RuleDetailViewDTO ruleDetailViewDTO = new RuleDetailViewDTO();
                ruleDetailViewDTO.setDevName(devName);
                ruleDetailViewDTO.setDevNameCode(devCode);
                ruleDetailViewDTO.setPropName(propInsName);
                ruleDetailViewDTO.setPropNameCode(propCode);
                ruleDetailViewDTO.setFormulaShow(formula);
                list.add(ruleDetailViewDTO);
            }
        }
        return list;
    }

    /**
     * 根据 dataCode 获取实例名称
     *
     * @param dataCode 实例编码
     * @return 实例名称
     */
    public String getInsNameByDataCode(String dataCode) {
        String url = dataTwinsUrl + "/common/ins/def"; // 接口地址
        Map<String, String> params = new HashMap<>();
        params.put("dataCode", dataCode);

        // 发送 GET 请求并获取响应
        String response = HttpClientUtil.doGet(url, params);

        // 解析响应
        JSONObject jsonResponse = JSONObject.parseObject(response);
        if (jsonResponse.getInteger("code") == 200) {
            JSONObject data = jsonResponse.getJSONObject("data");
            return data.getString("insName"); // 返回 insName
        } else {
            log.error("获取实例定义信息失败: {}", jsonResponse.getString("msg"));
            return null; // 或者抛出异常
        }
    }

    /**
     * 根据 dataCode 获取属性名称
     *
     * @param dataCode 数据编码
     * @return 属性名称(propName)
     */
    public String getPropNameByDataCode(String dataCode) {
        String url = dataTwinsUrl + "/prop/all/rt"; // 接口地址

        // 构建请求体 - JSON数组格式
        JSONArray requestBody = new JSONArray();
        requestBody.add(dataCode);

        // 发送 POST 请求并获取响应
        String response = null;
        try {
            response = HttpClientUtil.postJson(url, requestBody);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 解析响应
        JSONObject jsonResponse = JSONObject.parseObject(response);
        if (jsonResponse.getInteger("code") == 200) {
            JSONObject data = jsonResponse.getJSONArray("data").getJSONObject(0);
            return data.getString("propName");
        } else {
            log.error("获取属性名称失败: {}", jsonResponse.getString("msg"));
            return null;
        }
    }

    private RuleDetailViewDTO buildRuleDetailViewDTO(RuleDetail ruleDetail, String formula) {
        RuleDetailViewDTO dto = new RuleDetailViewDTO();

        // 设置规则类型（实例级/模型级）
        dto.setRuleType(ruleDetail.getRuleType());

        // 设置计算类型
        dto.setCalType(ruleDetail.getCalType());

        // 设置节点（模型）信息
        dto.setDevName(ruleDetail.getDevName());
        dto.setDevNameCode(ruleDetail.getDevCode());

        // 设置属性信息
        dto.setPropName(ruleDetail.getPropName());
        dto.setPropNameCode(ruleDetail.getDevProperty());

        // 设置指标集信息
        dto.setRuleColId(String.valueOf(ruleDetail.getRuleColId()));

        // 设置项目ID
        dto.setProjectId(ruleDetail.getProjectId());

        // 设置公式
        dto.setFormulaShow(formula);

        return dto;
    }


    public static List<DigitalTwinTreeVO> mergeLists(List<DigitalTwinTreeVO> list1, List<DigitalTwinTreeVO> list2) {
        // 创建一个Map来存储第一个列表的数据，方便通过dataCode快速查找
        Map<String, DigitalTwinTreeVO> map = new HashMap<>();
        for (DigitalTwinTreeVO item : list1) {
            map.put(item.getDataCode(), item);
        }

        // 创建一个新的列表来保存最终的结果
        List<DigitalTwinTreeVO> mergedList = new ArrayList<>(list1);

        // 遍历第二个列表
        for (DigitalTwinTreeVO item : list2) {
            // 检查是否已经存在于map中
            if (map.containsKey(item.getDataCode())) {
                // 如果存在，则合并children
                DigitalTwinTreeVO existingItem = map.get(item.getDataCode());
                existingItem.getChildren().addAll(item.getChildren());
            } else {
                // 如果不存在，则添加到新的列表中
                mergedList.add(item);
            }
        }

        return mergedList;
    }

    @Override
    @CacheEvict(cacheNames = MODEL_TREE_CACHE_NAMES, key = "#dataCode")
    public void clearModelTreeCache(String dataCode) {

    }

    @Override
    @CacheEvict(cacheNames = INS_TREE_CACHE_NAMES, key = "#dataCode")
    public void clearInsTreeCache(String dataCode) {

    }

    @Override
    public boolean verifyExpression(RuleDetailVerifyExpressionDTO dto) {

        // TODO 高卫东 修改为jep校验

        try {
            List<String> afterReplaceRuleFormula = Lists.newArrayList();
            dto.getRuleFormula().forEach(s -> {
                afterReplaceRuleFormula.add(s
                        // 替换函数
                        .replaceFirst("^#\\[(.*)\\]$", "$1")
                        // 替换变量
                        .replaceFirst("^@\\[(.*)\\]$", "$1"));
            });
            String expression = afterReplaceRuleFormula.stream().collect(Collectors.joining());
            AviatorEvaluator.validate(expression);
            return true;
        } catch (Exception e) {
            log.info("指标表达式校验不通过，", e);
            throw new BizException("指标表达式校验不通过");
        }
    }

    @Override
    @Transactional
    public Boolean toggle(Integer activeState, List<Long> ids) {
        LambdaUpdateWrapper<RuleDetail> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .in(RuleDetail::getId, ids)
                .set(RuleDetail::getActiveState, activeState);

        // 获取更新前的规则
        List<RuleDetail> oldRules = listByIds(ids);
        boolean updated = update(updateWrapper);
        // 发布启用/禁用事件
        RuleChangeEvent.ChangeType changeType = ActiveStateEnum.ENABLE.getValue() == activeState ?
                RuleChangeEvent.ChangeType.ENABLE : RuleChangeEvent.ChangeType.DISABLE;

        oldRules.forEach(oldRule -> {
            RuleDetail newRule = new RuleDetail();
            BeanUtils.copyProperties(oldRule, newRule);
            newRule.setActiveState(activeState);
            newRule.setUpdateTime(new Date());
            publishRuleChangeEvent(newRule, oldRule, changeType);
        });


        return updated;
    }

    @Override
    public List<RuleDetailVO> ruleList(String type) {
        LambdaQueryWrapper<RuleDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RuleDetail::getCalType, type);
        List<RuleDetail> ruleDetails = ruleDetailDao.selectList(wrapper);
        return RuleDetailConvertor.INSTANCE.entities2Vos(ruleDetails);
    }

    @Override
    @Transactional
    public void syncAllFormulaToInstance() {
        log.info("开始同步所有公式到实例表");
        StopWatch stopWatch = new StopWatch("规则公式同步");

        // 1. 获取所有公式
        stopWatch.start("查询公式");
        List<RuleDetailVo> allFormulas = ruleDetailDao.getAllFormulaDetail();
        int totalFormulas = allFormulas.size();
        stopWatch.stop();
        log.info("共查询到 {} 条公式", totalFormulas);

        if (totalFormulas == 0) {
            log.info("没有需要同步的公式，任务结束");
            return;
        }

        // 2. 按公式类型分组处理
        stopWatch.start("处理公式同步");
        int instanceFormulaCount = 0;
        int modelFormulaCount = 0;
        int successCount = 0;
        int failCount = 0;

        for (RuleDetailVo formula : allFormulas) {
            try {
                // 确保公式格式正确
                if (formula.getRuleFormula() != null) {
                    String convert = FormulaUtils.convert(formula.getRuleFormula());
                    formula.setRuleFormula(convert);

                    // 根据公式类型处理
                    if (RuleTypeEnum.INS.getValue() == formula.getRuleType()) {
                        // 实例级公式：直接保存到实例表
                        instanceFormulaCount++;

                        // 创建单个实例公式的FormulaDetail对象
                        FormulaDetail insFormula = new FormulaDetail(
                                formula.getDevCode(),
                                formula.getDevProperty(),
                                formula.getRuleFormula()
                        );

                        // 使用现有的batchSaveOrUpdateInstanceTable方法处理单个实例
                        List<FormulaDetail> singleFormulaList = Collections.singletonList(insFormula);
                        batchSaveOrUpdateInstanceTable(singleFormulaList, formula, Collections.emptyMap());

                        successCount++;
                    } else if (RuleTypeEnum.MODEL.getValue() == formula.getRuleType()) {
                        // 模型级公式：转换为实例公式后保存
                        modelFormulaCount++;

                        // 提取公式变量
                        List<String> varList = FormulaUtils.getVarList(formula.getRuleFormula());
                        varList.add(formula.getDevProperty());

                        // 使用现有方法处理模型公式
                        Model2InsFormula(formula, GlobalUtil.calculateMap, varList); // 这个方法内部会调用batchSaveOrUpdateInstanceTable

                        successCount++;
                    }
                }
            } catch (Exception e) {
                log.error("处理公式失败, ID: {}, 错误: {}", formula.getId(), e.getMessage(), e);
                failCount++;
            }
        }
        stopWatch.stop();

        log.info("公式同步完成: 总计 {} 条, 实例级公式 {} 条, 模型级公式 {} 条, 成功 {} 条, 失败 {} 条",
                totalFormulas, instanceFormulaCount, modelFormulaCount, successCount, failCount);
        log.info(stopWatch.prettyPrint());
    }


    /**
     * 查询并填充实例属性数据
     *
     * @param convertor 转换器
     * @param table     数据填充对象
     * @param dataCodes 节点实例数字化编码
     */
    private void splitQueryAndFillInsPropData(DigitalTwinConvertor convertor,
                                              Table<String, String, DigitalTwinTreeVO> table,
                                              List<String> dataCodes, String taskName) {

        StopWatch stopWatch = new StopWatch();

        InfoListQueryVo dynamicInfoListQueryVo = InfoListQueryVo.builder()
                .dataCodes(Sets.newHashSet(dataCodes))
                .propGroups(Lists.newArrayList(PropertyGroupEnum.Dynamic.getTag()))
                .pageNumber(1)
                .pageSize(Integer.MAX_VALUE)
                .build();
        stopWatch.start(String.join("-", taskName, "动态属性请求"));
        R<List<InsVO>> dynamicPropR = insService.batchAll(dynamicInfoListQueryVo);
        stopWatch.stop();
        stopWatch.start(String.join("-", taskName, "动态属性结果组装"));
        if (Objects.equals(R.OK().getCode(), dynamicPropR.getCode())) {
            List<InsVO> insVOList = dynamicPropR.getData();
            Table<String, String, List<DigitalTwinTreeVO>> propTable = HashBasedTable.create();
            insVOList.forEach(i -> {
                // 如果有动态属性
                List<DypropInsVO> dynamicProperties = i.getDynamicProperties();
                if (!CollectionUtils.isEmpty(dynamicProperties)) {
                    propTable.put(dynamicProperties.get(0).getInsDataCode(),
                            PropertyGroupEnum.Dynamic.getTag(),
                            convertor.dypropInsVOs2DigitalTwinTreeVOs(dynamicProperties, PropertyGroupEnum.Dynamic.getTag()));
                }
            });

            // 遍历将动态、静态属性绑定给树对象
            Map<String, Map<String, List<DigitalTwinTreeVO>>> dataCodeMap = propTable.rowMap();
            dataCodeMap.forEach((k, v) -> {
                // 构建动态属性根简化对象
                DigitalTwinTreeVO dynamicPropVO = DigitalTwinTreeVO.builder()
                        .name(PropertyGroupEnum.Dynamic.getDesc())
                        .nodeType(joinPropGroupField(PropertyGroupEnum.Dynamic.getTag(), EMPTY_STR))
                        .dataCode(joinPropGroupField(k, CENTER_BAR)).children(v.get(PropertyGroupEnum.Dynamic.getTag())).build();
                table.row(k).values().forEach(d -> {
                    // 如果当前节点不存在存在子节点
                    if (CollectionUtils.isEmpty(d.getChildren())) {
                        d.setChildren(Lists.newArrayList(dynamicPropVO));
                    }
                    // 如果当前节点存在子节点
                    else {
                        d.getChildren().add(0, dynamicPropVO);
                    }
                });
            });

        } else {
            log.error("获取动态属性数据失败：{}", dynamicPropR);
            throw new BizException("获取动态属性数据失败");
        }
        stopWatch.stop();

        InfoListQueryVo staticInfoListQueryVo = InfoListQueryVo.builder()
                .dataCodes(Sets.newHashSet(dataCodes))
                .propGroups(Lists.newArrayList(PropertyGroupEnum.Static.getTag()))
                .pageNumber(1)
                .pageSize(Integer.MAX_VALUE)
                .build();
        stopWatch.start(String.join("-", taskName, "静态属性请求"));
        R<List<InsVO>> staticPropR = insService.batchAll(staticInfoListQueryVo);
        stopWatch.stop();
        stopWatch.start(String.join("-", taskName, "静态属性结果组装"));
        if (Objects.equals(R.OK().getCode(), staticPropR.getCode())) {
            List<InsVO> insVOList = staticPropR.getData();
            Table<String, String, List<DigitalTwinTreeVO>> propTable = HashBasedTable.create();
            insVOList.forEach(i -> {
                // 如果有静态属性
                List<StpropInsVO> staticProperties = i.getStaticProperties();
                if (!CollectionUtils.isEmpty(staticProperties)) {
                    propTable.put(staticProperties.get(0).getInsDataCode(),
                            PropertyGroupEnum.Static.getTag(),
                            convertor.stpropInsVOs2DigitalTwinTreeVOs(staticProperties, PropertyGroupEnum.Static.getTag()));
                }
            });

            // 遍历将动态、静态属性绑定给树对象
            Map<String, Map<String, List<DigitalTwinTreeVO>>> dataCodeMap = propTable.rowMap();
            dataCodeMap.forEach((k, v) -> {
                // 构建静态属性根简化对象
                DigitalTwinTreeVO staticPropVO = DigitalTwinTreeVO.builder()
                        .name(PropertyGroupEnum.Static.getDesc())
                        .nodeType(joinPropGroupField(PropertyGroupEnum.Static.getTag(), EMPTY_STR))
                        .dataCode(joinPropGroupField(k, CENTER_BAR)).children(v.get(PropertyGroupEnum.Static.getTag())).build();
                table.row(k).values().forEach(d -> {
                    // 如果当前节点不存在存在子节点
                    if (CollectionUtils.isEmpty(d.getChildren())) {
                        d.setChildren(Lists.newArrayList(staticPropVO));
                    }
                    // 如果当前节点存在子节点
                    else {
                        d.getChildren().add(0, staticPropVO);
                    }
                });
            });

        } else {
            log.error("获取静态属性数据失败：{}", staticPropR);
            throw new BizException("获取静态属性数据失败");
        }

        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        log.error("查询动态静态属性数据*******************************");
        log.debug("查询的数字化编码：{}", dataCodes);
    }


    @Override
    public List<SelectOptionVO> getIndicatorList() {
        List<RuleDetailVo> allFormulaDetail = ruleDetailDao.getAllFormulaDetail();
        ArrayList<SelectOptionVO> selectOptionVOS = new ArrayList<>();
        for (RuleDetailVo ruleDetailVo : allFormulaDetail) {
            selectOptionVOS.add(SelectOptionVO.builder().value(ruleDetailVo.getDevProperty()).label(ruleDetailVo.getDevName() + "/" + ruleDetailVo.getPropName()).build());
        }
        return selectOptionVOS;
    }

    @Override
    public boolean deleteByIds(List<Long> ids) {
        List<RuleDetail> rules = listByIds(ids);
        // 发布删除事件
        rules.forEach(rule ->
                publishRuleChangeEvent(rule, null, RuleChangeEvent.ChangeType.DELETE));
        boolean removed = removeByIds(ids);
        return removed;
    }

    /**
     * 批量删除 rule_detail_instance 表中对应的实例规则（逻辑删除）
     * 替代原有的 removeBatchFromRedis 方法
     *
     * @param ruleDetails 规则详情列表
     */
    public void deleteBatchFromRuleDetailInstance(List<RuleDetailVo> ruleDetails) {
        if (CollectionUtils.isEmpty(ruleDetails)) {
            log.warn("没有提供任何规则详情，不执行删除操作");
            return;
        }
        try {
            // 1. 收集所有需要删除的规则的 ID
            List<Long> ruleDetailIds = ruleDetails.stream()
                    .map(RuleDetailVo::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (ruleDetailIds.isEmpty()) {
                log.warn("规则详情列表中没有有效的ID，不执行删除操作");
                return;
            }

            // 2. 构建逻辑删除条件
            boolean result = ruleDetailInstanceService.remove(
                    new QueryWrapper<RuleDetailInstance>()
                            .in("rule_detail_id", ruleDetailIds)
            );

            // 3. 记录删除结果
            if (result) {
                log.info("成功逻辑删除 rule_detail_instance 表中关联的规则实例，关联规则ID: {}", ruleDetailIds);
            } else {
                log.warn("尝试逻辑删除 rule_detail_instance 表中的规则实例未完全成功，关联规则ID: {}", ruleDetailIds);
            }
        } catch (Exception e) {
            log.error("逻辑删除 rule_detail_instance 表数据失败，错误: {}", e.getMessage(), e);
            throw new BizException("批量逻辑删除实例规则失败: " + e.getMessage());
        }
    }


    /**
     * 批量修改 rule_detail_instance 表中对应的实例规则（逻辑删除）
     * 替代原有的 removeBatchFromRedis 方法
     *
     * @param ruleDetails 规则详情列表
     */
    public void removeBatchFromRuleDetailInstance(List<RuleDetailVo> ruleDetails) {
        if (CollectionUtils.isEmpty(ruleDetails)) {
            log.warn("没有提供任何规则详情，不执行删除操作");
            return;
        }
        try {
            // 1. 收集所有需要删除的规则的 ID
            List<Long> ruleDetailIds = ruleDetails.stream()
                    .map(RuleDetailVo::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (ruleDetailIds.isEmpty()) {
                log.warn("规则详情列表中没有有效的ID，不执行删除操作");
                return;
            }

            // 2. 构建逻辑删除条件
            boolean result = ruleDetailInstanceService.remove(
                    new QueryWrapper<RuleDetailInstance>()
                            .in("rule_detail_id", ruleDetailIds)
            );

            // 3. 记录删除结果
            if (result) {
                log.info("成功逻辑删除 rule_detail_instance 表中关联的规则实例，关联规则ID: {}", ruleDetailIds);
            } else {
                log.warn("尝试逻辑删除 rule_detail_instance 表中的规则实例未完全成功，关联规则ID: {}", ruleDetailIds);
            }
        } catch (Exception e) {
            log.error("逻辑删除 rule_detail_instance 表数据失败，错误: {}", e.getMessage(), e);
            throw new BizException("批量逻辑删除实例规则失败: " + e.getMessage());
        }
    }
    /**
     * 模型计算公式转化为实例计算公式
     *
     * @param ruleDetailVoList
     * @return List<RuleDetailEntity>
     */
    private void getInsRuleDetailList(List<RuleDetailVo> ruleDetailVoList) {
        for (RuleDetailVo ruleDetailVo : ruleDetailVoList) {
            getInsRuleDetail(ruleDetailVo);
        }
    }

    public void getInsRuleDetail(RuleDetailVo ruleDetailVo) {
        if (ObjectUtil.isNotEmpty(ruleDetailVo.getRuleFormula())) {
            ruleDetailVo.setRuleFormula(FormulaUtils.convert(ruleDetailVo.getRuleFormula()));
            if (RuleTypeEnum.INS.getValue() == ruleDetailVo.getRuleType()) {
                //将数据存储到mysql的rule_detail_instance表中
                ruleDetailInstanceService.saveToRuleDetailInstance(ruleDetailVo);
            } else if (RuleTypeEnum.MODEL.getValue() == ruleDetailVo.getRuleType()) {
                //将库中的字符串数组转化为标准格式计算公式
                String ruleFormula = ruleDetailVo.getRuleFormula();
                //提取该公式的所有变量,包括输出变量
                List<String> varList = FormulaUtils.getVarList(ruleFormula);
                varList.add(ruleDetailVo.getDevProperty());
                //将该模型公式转化的实例公式加入到calculateMap
                Model2InsFormula(ruleDetailVo, GlobalUtil.calculateMap, varList);
            }
        }

    }

   private void Model2InsFormula(RuleDetailVo ruleDetailVo, Map<String, Map<DevPropertyKey, DevPropertyValue>> calculateMap, List<String> varList) {
       try {
           // 1. 批量获取所有模型的实例映射和名称映射
           ModelInstanceMappingResult mappingResult = batchGetModelInstanceMappings(
                   ruleDetailVo.getProjectCode(),
                   varList
           );

           // 2. 获取目标模型的子节点映射
           Map<String, Map<String, List<String>>> insNodeMap = getInstanceNodeMappings(
                   ruleDetailVo.getDevProperty(),
                   mappingResult.getModelInsMap().getOrDefault(ruleDetailVo.getDevProperty(), new HashMap<>())
           );

           // 3. 生成实例公式列表
           List<FormulaDetail> formulaList = getInsFormulaList(
                   ruleDetailVo.getDevCode(),
                   ruleDetailVo.getDevProperty(),
                   mappingResult.getModelInsMap(),
                   insNodeMap,
                   ruleDetailVo.getRuleFormula()
           );

           // 4. 批量写入mysql的rule_detail_instance表
//           batchSaveToRedis(formulaList, ruleDetailVo, mappingResult.getDevPropertyNameMap());
           batchSaveOrUpdateInstanceTable(formulaList, ruleDetailVo, mappingResult.getDevPropertyNameMap());
       } catch (Exception e) {
           log.error("处理模型公式失败: modelCode={}, error={}",
                   ruleDetailVo.getDevProperty(), e.getMessage(), e);
           throw new BizException("处理模型公式失败: " + e.getMessage());
       }
   }

    /**
     * 将生成的实例公式批量保存或更新到 rule_detail_instance 表。
     * 更新逻辑基于 ruleDetailId, devProperty, ruleColId 和继承自源模型的 ruleType。
     *
     * @param formulaList       生成的实例公式列表 (来自 getInsFormulaList)。
     * @param modelRuleDetailVo 原始的模型级 RuleDetailVo，包含元数据（其 ruleType 应为 1）。
     * @param nameMap           包含编码对应名称的 Map。
     */
    private void batchSaveOrUpdateInstanceTable(List<FormulaDetail> formulaList,
                                                RuleDetailVo modelRuleDetailVo,
                                                Map<String, String> nameMap) {

        if (CollectionUtils.isEmpty(formulaList)) {
            log.warn("没有为模型规则 ID {} 生成实例公式，无需处理。", modelRuleDetailVo.getId());
            return;
        }

        // 1. 提取关键信息用于查询
        Long modelRuleDetailId = modelRuleDetailVo.getId();
        Long modelRuleColId = modelRuleDetailVo.getRuleColId();
        Integer sourceRuleType = modelRuleDetailVo.getRuleType(); // <-- 获取源模型的 ruleType (应为 1)
        Set<String> instanceDevProperties = formulaList.stream()
                .map(FormulaDetail::getDevProperty)
                .collect(Collectors.toSet());

        // 2. 使用自定义XML查询方法查询已存在的实例
        Map<String, RuleDetailInstance> existingInstanceMap = new HashMap<>();
        if (!instanceDevProperties.isEmpty()) {
            // 使用自定义DAO方法替代原来的查询
            List<RuleDetailInstance> existingInstances = ruleDetailInstanceService.findInstancesByKeyFields(
                    modelRuleDetailId,
                    modelRuleColId,
                    sourceRuleType,
                    instanceDevProperties
            );

            log.info("为模型规则 ID {} 找到 {} 条已存在的实例记录 (Type={})。",
                    modelRuleDetailId, existingInstances.size(), sourceRuleType);
            // 将查询结果放入 Map，方便后续查找，Key 为 devProperty
            existingInstanceMap = existingInstances.stream()
                    .collect(Collectors.toMap(RuleDetailInstance::getDevProperty, Function.identity()));
            log.info("为模型规则 ID {} 找到 {} 条已存在的实例记录 (Type={})。", modelRuleDetailId, existingInstances.size(), sourceRuleType);
        }

        // 3. 区分需要插入和需要更新的列表
        List<RuleDetailInstance> instancesToInsert = new ArrayList<>();
        List<RuleDetailInstance> instancesToUpdate = new ArrayList<>();
        Date now = new Date();

        for (FormulaDetail generatedFormula : formulaList) {
            RuleDetailInstance existingInstance = existingInstanceMap.get(generatedFormula.getDevProperty());

            if (existingInstance != null) {
                // --- 情况：实例已存在，执行更新 ---
                log.info("准备更新实例，ruleDetailId={}, devProperty={}, ruleType={}", modelRuleDetailId, generatedFormula.getDevProperty(), sourceRuleType);
                // 使用查出来的 existingInstance 对象进行更新
                existingInstance.setRuleName(modelRuleDetailVo.getRuleName());
                existingInstance.setRuleDes(modelRuleDetailVo.getRuleDes());
                existingInstance.setRuleType(sourceRuleType); // <-- 更新时也确保 ruleType 与源模型一致
                existingInstance.setCalType(modelRuleDetailVo.getCalType());
                existingInstance.setProjectId(modelRuleDetailVo.getProjectId());
                existingInstance.setRuleColId(modelRuleColId); // 保持 ruleColId
                existingInstance.setActiveState(modelRuleDetailVo.getActiveState());
                existingInstance.setDevCode(generatedFormula.getDevCode());
                existingInstance.setDevName(nameMap.getOrDefault(generatedFormula.getDevCode(), modelRuleDetailVo.getDevName()));
                // devProperty 保持不变
                existingInstance.setDevProperty(generatedFormula.getDevProperty());
                existingInstance.setPropName(nameMap.getOrDefault(generatedFormula.getDevProperty(), modelRuleDetailVo.getPropName()));
                existingInstance.setRuleFormula(generatedFormula.getFormula());
                existingInstance.setRuleFormulaShow(modelRuleDetailVo.getRuleFormulaShow()); // 或根据需要生成
                existingInstance.setUpdateTime(now);
                existingInstance.setDeleted(modelRuleDetailVo.getDeleted());
                // existingInstance.setVersion(...);

                instancesToUpdate.add(existingInstance);
            } else {
                // --- 情况：实例不存在，执行插入 ---
                log.trace("准备插入新实例，ruleDetailId={}, devProperty={}, ruleType={}", modelRuleDetailId, generatedFormula.getDevProperty(), sourceRuleType);
                RuleDetailInstance instance = new RuleDetailInstance();
                // 填充所有必要的字段
                instance.setRuleDetailId(modelRuleDetailId);
                instance.setRuleName(modelRuleDetailVo.getRuleName());
                instance.setRuleDes(modelRuleDetailVo.getRuleDes());
                instance.setRuleType(sourceRuleType); // <-- 设置为源模型的 ruleType
                instance.setCalType(modelRuleDetailVo.getCalType());
                instance.setProjectId(modelRuleDetailVo.getProjectId());
                instance.setRuleColId(modelRuleColId);
                instance.setActiveState(modelRuleDetailVo.getActiveState());
                instance.setDevCode(generatedFormula.getDevCode());
                instance.setDevName(nameMap.getOrDefault(generatedFormula.getDevCode(), modelRuleDetailVo.getDevName()));
                // devProperty 保持不变
                instance.setDevProperty(generatedFormula.getDevProperty());
                instance.setPropName(nameMap.getOrDefault(generatedFormula.getDevProperty(), modelRuleDetailVo.getPropName()));
                instance.setRuleFormula(generatedFormula.getFormula());
                instance.setRuleFormulaShow(modelRuleDetailVo.getRuleFormulaShow()); // 暂时与 formula 相同
                instance.setCreateTime(now);
                instance.setUpdateTime(now);
                instance.setDeleted(modelRuleDetailVo.getDeleted());
                instancesToInsert.add(instance);
            }
        }

        // 4. 执行批量数据库操作 (保持不变)
        if (!instancesToInsert.isEmpty()) {
            try {
                boolean insertSuccess = ruleDetailInstanceService.saveBatch(instancesToInsert);
                log.info("批量插入 {} 条新实例公式 (Type={})，模型规则 ID: {}", instancesToInsert.size(), sourceRuleType, modelRuleDetailId);
                // ... rest of insert handling ...
            } catch (Exception e) {
                log.error("批量插入 rule_detail_instance (Type={}) 失败，模型规则 ID: {}. 错误: {}", sourceRuleType, modelRuleDetailId, e.getMessage(), e);
                throw new BizException("批量保存新实例公式到数据库失败", e);
            }
        }

        // 执行批量更新操作
        if (!instancesToUpdate.isEmpty()) {
            try {
                // 使用条件更新替代updateBatchById
                boolean updateSuccess = true;

                for (RuleDetailInstance instance : instancesToUpdate) {
                    // 使用四个关键字段作为更新条件
                    boolean result = ruleDetailInstanceService.update(instance,
                            new UpdateWrapper<RuleDetailInstance>()
                                    .eq("rule_detail_id", instance.getRuleDetailId())
                                    .eq("dev_property", instance.getDevProperty())
                                    .eq("rule_col_id", instance.getRuleColId())
                                    .eq("rule_type", instance.getRuleType()));

                    if (!result) {
                        updateSuccess = false;
                        log.warn("更新实例失败: ruleDetailId={}, devProperty={}, ruleColId={}, ruleType={}",
                                instance.getRuleDetailId(), instance.getDevProperty(),
                                instance.getRuleColId(), instance.getRuleType());
                    }
                }

                log.info("批量更新 {} 条现有实例公式，结果: {}",
                        instancesToUpdate.size(), updateSuccess ? "成功" : "部分失败");
            } catch (Exception e) {
                log.error("批量更新 rule_detail_instance 失败，规则ID: {}. 错误: {}",
                        modelRuleDetailId, e.getMessage(), e);
                throw new BizException("批量更新现有实例公式到数据库失败", e);
            }
        }
    }

    /**
     * 获取实例节点映射
     * @param modelCode 模型编码
     * @param instanceMap 实例映射
     * @return 实例节点映射
     */
    private Map<String, Map<String, List<String>>> getInstanceNodeMappings(
            String modelCode,
            Map<String, String> instanceMap) {

        Map<String, Map<String, List<String>>> insNodeMap = new HashMap<>();

        if (CollectionUtils.isEmpty(instanceMap)) {
            return insNodeMap;
        }

        // 并行处理每个实例的子节点
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (String insDataCode : instanceMap.keySet()) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 构建请求参数
                    Map<String, String> params = new HashMap<>();
                    params.put("dataCode", insDataCode);
                    params.put("hasSub", "true");
                    params.put("hasEq", "true");
                    params.put("hasPipe", "false");
                    params.put("withSelf", "true");

                    // 调用接口获取子节点
                    String insResult = HttpClientUtil.doGet(dataTwinsUrl + "/common/ins/tree", params);
                    JSONArray insNodeArray = CommonUtils.returnResultHandler(insResult);

                    if (insNodeArray != null) {
                        for (int i = 0; i < insNodeArray.size(); i++) {
                            JSONObject insNode = insNodeArray.getJSONObject(i);
                            List<String> insList = extractDataCodes(insNode);

                            // 线程安全地更新映射
                            synchronized (insNodeMap) {
                                CommonUtils.addDataToMap(insNodeMap, modelCode, insDataCode, insList);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("获取实例子节点失败: insDataCode={}, error={}",
                            insDataCode, e.getMessage(), e);
                }
            }, executor);

            futures.add(future);
        }

        // 等待所有异步任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        return insNodeMap;
    }



    /**
     * 批量获取模型实例映射
     */
    private ModelInstanceMappingResult batchGetModelInstanceMappings(String projectCode, List<String> modelCodes) {
        Map<String, Map<String, String>> modelInsMap = new HashMap<>();
        Map<String, String> devPropertyNameMap = new HashMap<>();

        // 构建请求参数
        JSONObject requestBody = new JSONObject();
        requestBody.put("insDataCode", projectCode);
        requestBody.put("propModelDataCodes", modelCodes);

        // 批量获取实例映射
        String result = HttpClientUtil.postJson(dataTwinsUrl + "/common/prop/ins", requestBody);
        JSONArray array = CommonUtils.returnResultHandler(result);

        if (array != null) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject jsObject = array.getJSONObject(i);
                String modelCode = jsObject.getString("propModelDataCode");
                String insDataCode = jsObject.getString("insDataCode");
                String propInsDataCode = jsObject.getString("propInsDataCode");
                String insName = jsObject.getString("insName");
                String propInsName = jsObject.getString("propInsName");

                // 存储模型-实例映射
                modelInsMap.computeIfAbsent(modelCode, k -> new HashMap<>())
                        .put(insDataCode, propInsDataCode);

                // 存储名称映射
                devPropertyNameMap.put(insDataCode, insName);
                devPropertyNameMap.put(propInsDataCode, propInsName);
            }
        }

        return new ModelInstanceMappingResult(modelInsMap, devPropertyNameMap);
    }
    private String getInsByModelCode(String url, List<String> modelList, String projectCode) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("insDataCode", projectCode);
        jsonObject.put("propModelDataCodes", modelList);
        return HttpClientUtil.postJson(url, jsonObject);
    }

    /*
     * <AUTHOR>
     * @Description //递归获取该DataCode的子节点信息
     * @Date 16:18 2024/7/13
     * @Param
     * @return
     **/

    private List<String> extractDataCodes(JSONObject jsonObj) {
        List<String> dataList = new ArrayList<>();

        Deque<JSONObject> stack = new ArrayDeque<>();
        stack.push(jsonObj);

        while (!stack.isEmpty()) {
            JSONObject node = stack.pop();
            dataList.add(node.getString("dataCode"));

            if (node.containsKey("children")) {
                JSONArray children = node.getJSONArray("children");
                if (!ObjectUtils.isEmpty(children))
                    for (int i = 0; i < children.size(); i++) {
                        stack.push(children.getJSONObject(i));
                    }
            }
        }

        return dataList;
    }

    private List<FormulaDetail> getInsFormulaList(String modelDevCode, String modelDevProp, Map<String, Map<String, String>> modelInsMap, Map<String, Map<String, List<String>>> insNodeMap, String ruleFormula) {
        List<FormulaDetail> list = new ArrayList<>();
        //获取到计算公式中目标模型对应实例设备和实例属性编码
        Map<String, String> modelDevPropMap = modelInsMap.getOrDefault(modelDevProp, new HashMap<>());
        //遍历
        for (Map.Entry<String, String> entry : modelDevPropMap.entrySet()) {
            String insDevCode = entry.getKey();
            String insDevProp = entry.getValue();
            String subFormula = ruleFormula;
            List<String> insNodeList = insNodeMap.getOrDefault(modelDevProp, new HashMap<>()).getOrDefault(insDevCode, new ArrayList<>());

            List<String> modelList = FormulaUtils.getVarList(subFormula);
            Map<String, List<String>> model2InsList = new HashMap<>();

            for (String model : modelList) {
                Map<String, String> innerMap = modelInsMap.getOrDefault(model, new HashMap<>());

                for (Map.Entry<String, String> innerEntry : innerMap.entrySet()) {
                    if (insNodeList.contains(innerEntry.getKey())) {
                        model2InsList.computeIfAbsent(model, k -> new ArrayList<>()).add(innerEntry.getValue());
                    }
                }
            }

            for (Map.Entry<String, List<String>> entrySet : model2InsList.entrySet()) {
                String modelCode = prefix + entrySet.getKey() + suffix;
                String insListStr = String.join(",", entrySet.getValue().stream().map(code -> prefix + code + suffix).toArray(String[]::new));
                subFormula = subFormula.replace(modelCode, insListStr);
            }

            list.add(new FormulaDetail(insDevCode, insDevProp, subFormula));
        }

        return list;
    }

    private List<AttrObject> getTargetAttrList(RuleDetailInsertDTO ruleDetailInsertDTO) {
        ArrayList<AttrObject> attrObjects = new ArrayList<>();
        String devProperty = ruleDetailInsertDTO.getDevProperty();
        if (ruleDetailInsertDTO.getRuleType() == 1) {
            ArrayList<String> list = new ArrayList<>();
            list.add(devProperty);
            Long projectId = ruleDetailInsertDTO.getProjectId();
            DataProjectVO vo = dataProjectService.getVoById(projectId);
            String projectCode = vo.getProjectCode();
            //说明是模型级指标，需要获取该模型所有的属性列表，将其放入指标集的attr_list中
            String url = dataTwinsUrl + "/common/prop/ins";
            String result = getInsByModelCode(url, list, projectCode);
            JSONArray array = CommonUtils.returnResultHandler(result);
            for (int i = 0; i < array.size(); i++) {
                JSONObject jsonObject = array.getJSONObject(i);
                String propInsDataCode = jsonObject.getString("propInsDataCode");
                String propInsName = jsonObject.getString("propInsName");
                String insName = jsonObject.getString("insName");
                AttrObject attrObject = new AttrObject();
                attrObject.setAttrCode(propInsDataCode);
                attrObject.setAttrName(insName + "/" + propInsName);
                ;
                attrObjects.add(attrObject);

            }

        } else {
            AttrObject attrObject = new AttrObject();
            attrObject.setAttrCode(devProperty);
            attrObject.setAttrName(ruleDetailInsertDTO.getDevName() + "/" + ruleDetailInsertDTO.getPropName());
            ;
            attrObjects.add(attrObject);
        }
        return attrObjects;
    }

    /**
     * 从 rule_detail_instance 表中获取公式
     * 查询优先级：实例级公式 > 模型级公式
     *
     * @param propCode 属性编码
     * @return 公式内容字符串
     */
    public String getFormulaFromInstance(String propCode) {
        if (StrUtil.isBlank(propCode)) {
            log.warn("属性编码为空，无法获取公式");
            return null;
        }

        try {
            // 1. 首先查询实例级公式(rule_type=0)
            RuleDetailInstance instance = ruleDetailInstanceService.getOne(
                    new QueryWrapper<RuleDetailInstance>()
                            .eq("dev_property", propCode)
                            .eq("rule_type", 0) // 实例级公式
                            .eq("active_state", 0) // 已激活
                            .eq("deleted", 0) // 未删除
                            .last("LIMIT 1")
            );

            // 2. 如果没找到实例级公式，查询模型级公式(rule_type=1)
            if (instance == null) {
                instance = ruleDetailInstanceService.getOne(
                        new QueryWrapper<RuleDetailInstance>()
                                .eq("dev_property", propCode)
                                .eq("rule_type", 1) // 模型级公式
                                .eq("active_state", 0) // 已激活
                                .eq("deleted", 0) // 未删除
                                .last("LIMIT 1")
                );
            }

            // 3. 返回找到的公式
            if (instance != null) {
                log.debug("从 rule_detail_instance 表中找到属性 {} 的公式", propCode);
                return instance.getRuleFormula();
            } else {
                log.debug("在 rule_detail_instance 表中未找到属性 {} 的公式", propCode);
                return null;
            }
        } catch (Exception e) {
            log.error("从 rule_detail_instance 表查询公式失败，属性编码: {}, 错误: {}", propCode, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从 rule_detail_instance 表中获取公式详情对象
     * 查询优先级：实例级公式 > 模型级公式
     *
     * @param propCode 属性编码
     * @param ruleColId 规则集ID
     * @return 公式详情对象
     */
    public RuleFormulaDetail getFormula(String propCode, Long ruleColId) {
        if (StrUtil.isBlank(propCode)) {
            return null;
        }

        try {
            // 1. 首先查询实例级公式
            QueryWrapper<RuleDetailInstance> instanceQuery = new QueryWrapper<RuleDetailInstance>()
                    .eq("dev_property", propCode)
                    .eq("rule_type", 0) // 实例级公式
                    .eq("active_state", 0) // 已激活
                    .eq("deleted", 0); // 未删除

            // 如果提供了规则集ID，则加入条件
            if (ruleColId != null) {
                instanceQuery.eq("rule_col_id", ruleColId);
            }

            instanceQuery.last("LIMIT 1");

            RuleDetailInstance instance = ruleDetailInstanceService.getOne(instanceQuery);

            // 2. 如果没找到实例级公式，查询模型级公式
            if (instance == null) {
                QueryWrapper<RuleDetailInstance> modelQuery = new QueryWrapper<RuleDetailInstance>()
                        .eq("dev_property", propCode)
                        .eq("rule_type", 1) // 模型级公式
                        .eq("active_state", 0) // 已激活
                        .eq("deleted", 0); // 未删除

                if (ruleColId != null) {
                    modelQuery.eq("rule_col_id", ruleColId);
                }

                modelQuery.last("LIMIT 1");

                instance = ruleDetailInstanceService.getOne(modelQuery);
            }

            // 3. 转换为RuleFormulaDetail对象返回
            if (instance != null) {
                RuleFormulaDetail formulaDetail = new RuleFormulaDetail();
                formulaDetail.setRuleName(instance.getRuleName());
                formulaDetail.setCalType(instance.getCalType());
                formulaDetail.setDevCode(instance.getDevCode());
                formulaDetail.setDevName(instance.getDevName());
                formulaDetail.setDevProperty(instance.getDevProperty());
                formulaDetail.setPropName(instance.getPropName());
                formulaDetail.setRuleFormula(instance.getRuleFormula());

                log.debug("成功从 rule_detail_instance 表获取到属性 {} 的公式详情", propCode);
                return formulaDetail;
            } else {
                log.debug("在 rule_detail_instance 表中未找到属性 {} 的公式详情", propCode);
                return null;
            }
        } catch (Exception e) {
            log.error("查询公式详情失败，属性编码: {}, 规则集ID: {}, 错误: {}", propCode, ruleColId, e.getMessage(), e);
            return null;
        }
    }


}

